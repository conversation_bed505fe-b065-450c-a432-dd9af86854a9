{"version": 3, "sources": ["../../../../src/shared/lib/lazy-dynamic/dynamic-bailout-to-csr.tsx"], "sourcesContent": ["'use client'\n\nimport type { ReactElement } from 'react'\nimport { BailoutToCSRError } from './bailout-to-csr'\n\ninterface BailoutToCSRProps {\n  reason: string\n  children: ReactElement\n}\n\n/**\n * If rendered on the server, this component throws an error\n * to signal Next.js that it should bail out to client-side rendering instead.\n */\nexport function BailoutToCSR({ reason, children }: BailoutToCSRProps) {\n  if (typeof window === 'undefined') {\n    throw new BailoutToCSRError(reason)\n  }\n\n  return children\n}\n"], "names": ["BailoutToCSR", "reason", "children", "window", "BailoutToCSRError"], "mappings": "AAAA;;;;;+BAcgBA;;;eAAAA;;;8BAXkB;AAW3B,SAASA,aAAa,KAAuC;IAAvC,IAAA,EAAEC,MAAM,EAAEC,QAAQ,EAAqB,GAAvC;IAC3B,IAAI,OAAOC,WAAW,aAAa;QACjC,MAAM,qBAA6B,CAA7B,IAAIC,+BAAiB,CAACH,SAAtB,qBAAA;mBAAA;wBAAA;0BAAA;QAA4B;IACpC;IAEA,OAAOC;AACT"}