{"version": 3, "sources": ["../../../src/shared/lib/hooks-client-context.shared-runtime.ts"], "sourcesContent": ["'use client'\n\nimport { createContext } from 'react'\nimport type { Params } from '../../server/request/params'\n\nexport const SearchParamsContext = createContext<URLSearchParams | null>(null)\nexport const PathnameContext = createContext<string | null>(null)\nexport const PathParamsContext = createContext<Params | null>(null)\n\nif (process.env.NODE_ENV !== 'production') {\n  SearchParamsContext.displayName = 'SearchParamsContext'\n  PathnameContext.displayName = 'PathnameContext'\n  PathParamsContext.displayName = 'PathParamsContext'\n}\n"], "names": ["PathParamsContext", "PathnameContext", "SearchParamsContext", "createContext", "process", "env", "NODE_ENV", "displayName"], "mappings": "AAAA;;;;;;;;;;;;;;;;;IAOaA,iBAAiB;eAAjBA;;IADAC,eAAe;eAAfA;;IADAC,mBAAmB;eAAnBA;;;uBAHiB;AAGvB,MAAMA,sBAAsBC,IAAAA,oBAAa,EAAyB;AAClE,MAAMF,kBAAkBE,IAAAA,oBAAa,EAAgB;AACrD,MAAMH,oBAAoBG,IAAAA,oBAAa,EAAgB;AAE9D,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;IACzCJ,oBAAoBK,WAAW,GAAG;IAClCN,gBAAgBM,WAAW,GAAG;IAC9BP,kBAAkBO,WAAW,GAAG;AAClC"}