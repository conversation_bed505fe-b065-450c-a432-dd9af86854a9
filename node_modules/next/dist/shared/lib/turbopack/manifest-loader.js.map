{"version": 3, "sources": ["../../../../src/shared/lib/turbopack/manifest-loader.ts"], "sourcesContent": ["import type {\n  EdgeFunctionDefinition,\n  MiddlewareManifest,\n} from '../../../build/webpack/plugins/middleware-plugin'\nimport type {\n  StatsAsset,\n  StatsChunk,\n  StatsChunkGroup,\n  StatsModule,\n  StatsCompilation as WebpackStats,\n} from 'webpack'\nimport type { BuildManifest } from '../../../server/get-page-files'\nimport type { AppBuildManifest } from '../../../build/webpack/plugins/app-build-manifest-plugin'\nimport type { PagesManifest } from '../../../build/webpack/plugins/pages-manifest-plugin'\nimport { pathToRegexp } from 'next/dist/compiled/path-to-regexp'\nimport type { ActionManifest } from '../../../build/webpack/plugins/flight-client-entry-plugin'\nimport type { NextFontManifest } from '../../../build/webpack/plugins/next-font-manifest-plugin'\nimport type { REACT_LOADABLE_MANIFEST } from '../constants'\nimport {\n  APP_BUILD_MANIFEST,\n  APP_PATHS_MANIFEST,\n  BUILD_MANIFEST,\n  INTERCEPTION_ROUTE_REWRITE_MANIFEST,\n  MIDDLEWARE_BUILD_MANIFEST,\n  MIDDLEWARE_MANIFEST,\n  NEXT_FONT_MANIFEST,\n  PAGES_MANIFEST,\n  SERVER_REFERENCE_MANIFEST,\n  TURBOPACK_CLIENT_MIDDLEWARE_MANIFEST,\n  WEBPACK_STATS,\n} from '../constants'\nimport { join, posix } from 'path'\nimport { readFile } from 'fs/promises'\nimport type { SetupOpts } from '../../../server/lib/router-utils/setup-dev-bundler'\nimport { deleteCache } from '../../../server/dev/require-cache'\nimport { writeFileAtomic } from '../../../lib/fs/write-atomic'\nimport { isInterceptionRouteRewrite } from '../../../lib/generate-interception-routes-rewrites'\nimport {\n  type ClientBuildManifest,\n  normalizeRewritesForBuildManifest,\n  srcEmptySsgManifest,\n  processRoute,\n  createEdgeRuntimeManifest,\n} from '../../../build/webpack/plugins/build-manifest-plugin'\nimport getAssetPathFromRoute from '../router/utils/get-asset-path-from-route'\nimport { getEntryKey, type EntryKey } from './entry-key'\nimport type { CustomRoutes } from '../../../lib/load-custom-routes'\nimport { getSortedRoutes } from '../router/utils'\nimport { existsSync } from 'fs'\nimport {\n  addMetadataIdToRoute,\n  addRouteSuffix,\n  removeRouteSuffix,\n} from '../../../server/dev/turbopack-utils'\nimport { tryToParsePath } from '../../../lib/try-to-parse-path'\nimport type { Entrypoints } from '../../../build/swc/types'\n\ninterface InstrumentationDefinition {\n  files: string[]\n  name: 'instrumentation'\n}\n\ntype TurbopackMiddlewareManifest = MiddlewareManifest & {\n  instrumentation?: InstrumentationDefinition\n}\n\ntype ManifestName =\n  | typeof MIDDLEWARE_MANIFEST\n  | typeof BUILD_MANIFEST\n  | typeof APP_BUILD_MANIFEST\n  | typeof PAGES_MANIFEST\n  | typeof WEBPACK_STATS\n  | typeof APP_PATHS_MANIFEST\n  | `${typeof SERVER_REFERENCE_MANIFEST}.json`\n  | `${typeof NEXT_FONT_MANIFEST}.json`\n  | typeof REACT_LOADABLE_MANIFEST\n\nconst getManifestPath = (\n  page: string,\n  distDir: string,\n  name: ManifestName,\n  type: string,\n  firstCall: boolean\n) => {\n  let manifestPath = posix.join(\n    distDir,\n    `server`,\n    type,\n    type === 'middleware' || type === 'instrumentation'\n      ? ''\n      : type === 'app'\n        ? page\n        : getAssetPathFromRoute(page),\n    name\n  )\n\n  if (firstCall) {\n    const isSitemapRoute = /[\\\\/]sitemap(.xml)?\\/route$/.test(page)\n    // Check the ambiguity of /sitemap and /sitemap.xml\n    if (isSitemapRoute && !existsSync(manifestPath)) {\n      manifestPath = getManifestPath(\n        page.replace(/\\/sitemap\\/route$/, '/sitemap.xml/route'),\n        distDir,\n        name,\n        type,\n        false\n      )\n    }\n    // existsSync is faster than using the async version\n    if (!existsSync(manifestPath) && page.endsWith('/route')) {\n      // TODO: Improve implementation of metadata routes, currently it requires this extra check for the variants of the files that can be written.\n      let metadataPage = addRouteSuffix(\n        addMetadataIdToRoute(removeRouteSuffix(page))\n      )\n      manifestPath = getManifestPath(metadataPage, distDir, name, type, false)\n    }\n  }\n\n  return manifestPath\n}\n\nasync function readPartialManifest<T>(\n  distDir: string,\n  name: ManifestName,\n  pageName: string,\n  type: 'pages' | 'app' | 'middleware' | 'instrumentation' = 'pages'\n): Promise<T> {\n  const page = pageName\n  const manifestPath = getManifestPath(page, distDir, name, type, true)\n  return JSON.parse(await readFile(posix.join(manifestPath), 'utf-8')) as T\n}\n\nexport class TurbopackManifestLoader {\n  private actionManifests: Map<EntryKey, ActionManifest> = new Map()\n  private appBuildManifests: Map<EntryKey, AppBuildManifest> = new Map()\n  private appPathsManifests: Map<EntryKey, PagesManifest> = new Map()\n  private buildManifests: Map<EntryKey, BuildManifest> = new Map()\n  private fontManifests: Map<EntryKey, NextFontManifest> = new Map()\n  private middlewareManifests: Map<EntryKey, TurbopackMiddlewareManifest> =\n    new Map()\n  private pagesManifests: Map<string, PagesManifest> = new Map()\n  private webpackStats: Map<EntryKey, WebpackStats> = new Map()\n  private encryptionKey: string\n\n  private readonly distDir: string\n  private readonly buildId: string\n\n  constructor({\n    distDir,\n    buildId,\n    encryptionKey,\n  }: {\n    buildId: string\n    distDir: string\n    encryptionKey: string\n  }) {\n    this.distDir = distDir\n    this.buildId = buildId\n    this.encryptionKey = encryptionKey\n  }\n\n  delete(key: EntryKey) {\n    this.actionManifests.delete(key)\n    this.appBuildManifests.delete(key)\n    this.appPathsManifests.delete(key)\n    this.buildManifests.delete(key)\n    this.fontManifests.delete(key)\n    this.middlewareManifests.delete(key)\n    this.pagesManifests.delete(key)\n    this.webpackStats.delete(key)\n  }\n\n  async loadActionManifest(pageName: string): Promise<void> {\n    this.actionManifests.set(\n      getEntryKey('app', 'server', pageName),\n      await readPartialManifest(\n        this.distDir,\n        `${SERVER_REFERENCE_MANIFEST}.json`,\n        pageName,\n        'app'\n      )\n    )\n  }\n\n  private async mergeActionManifests(manifests: Iterable<ActionManifest>) {\n    type ActionEntries = ActionManifest['edge' | 'node']\n    const manifest: ActionManifest = {\n      node: {},\n      edge: {},\n      encryptionKey: this.encryptionKey,\n    }\n\n    function mergeActionIds(\n      actionEntries: ActionEntries,\n      other: ActionEntries\n    ): void {\n      for (const key in other) {\n        const action = (actionEntries[key] ??= {\n          workers: {},\n          layer: {},\n        })\n        Object.assign(action.workers, other[key].workers)\n        Object.assign(action.layer, other[key].layer)\n      }\n    }\n\n    for (const m of manifests) {\n      mergeActionIds(manifest.node, m.node)\n      mergeActionIds(manifest.edge, m.edge)\n    }\n    for (const key in manifest.node) {\n      const entry = manifest.node[key]\n      entry.workers = sortObjectByKey(entry.workers)\n      entry.layer = sortObjectByKey(entry.layer)\n    }\n    for (const key in manifest.edge) {\n      const entry = manifest.edge[key]\n      entry.workers = sortObjectByKey(entry.workers)\n      entry.layer = sortObjectByKey(entry.layer)\n    }\n\n    return manifest\n  }\n\n  private async writeActionManifest(): Promise<void> {\n    const actionManifest = await this.mergeActionManifests(\n      this.actionManifests.values()\n    )\n    const actionManifestJsonPath = join(\n      this.distDir,\n      'server',\n      `${SERVER_REFERENCE_MANIFEST}.json`\n    )\n    const actionManifestJsPath = join(\n      this.distDir,\n      'server',\n      `${SERVER_REFERENCE_MANIFEST}.js`\n    )\n    const json = JSON.stringify(actionManifest, null, 2)\n    deleteCache(actionManifestJsonPath)\n    deleteCache(actionManifestJsPath)\n    await writeFileAtomic(actionManifestJsonPath, json)\n    await writeFileAtomic(\n      actionManifestJsPath,\n      `self.__RSC_SERVER_MANIFEST=${JSON.stringify(json)}`\n    )\n  }\n\n  async loadAppBuildManifest(pageName: string): Promise<void> {\n    this.appBuildManifests.set(\n      getEntryKey('app', 'server', pageName),\n      await readPartialManifest(\n        this.distDir,\n        APP_BUILD_MANIFEST,\n        pageName,\n        'app'\n      )\n    )\n  }\n\n  private mergeAppBuildManifests(manifests: Iterable<AppBuildManifest>) {\n    const manifest: AppBuildManifest = {\n      pages: {},\n    }\n    for (const m of manifests) {\n      Object.assign(manifest.pages, m.pages)\n    }\n    manifest.pages = sortObjectByKey(manifest.pages)\n    return manifest\n  }\n\n  private async writeAppBuildManifest(): Promise<void> {\n    const appBuildManifest = this.mergeAppBuildManifests(\n      this.appBuildManifests.values()\n    )\n    const appBuildManifestPath = join(this.distDir, APP_BUILD_MANIFEST)\n    deleteCache(appBuildManifestPath)\n    await writeFileAtomic(\n      appBuildManifestPath,\n      JSON.stringify(appBuildManifest, null, 2)\n    )\n  }\n\n  async loadAppPathsManifest(pageName: string): Promise<void> {\n    this.appPathsManifests.set(\n      getEntryKey('app', 'server', pageName),\n      await readPartialManifest(\n        this.distDir,\n        APP_PATHS_MANIFEST,\n        pageName,\n        'app'\n      )\n    )\n  }\n\n  private async writeAppPathsManifest(): Promise<void> {\n    const appPathsManifest = this.mergePagesManifests(\n      this.appPathsManifests.values()\n    )\n    const appPathsManifestPath = join(\n      this.distDir,\n      'server',\n      APP_PATHS_MANIFEST\n    )\n    deleteCache(appPathsManifestPath)\n    await writeFileAtomic(\n      appPathsManifestPath,\n      JSON.stringify(appPathsManifest, null, 2)\n    )\n  }\n\n  private async writeWebpackStats(): Promise<void> {\n    const webpackStats = this.mergeWebpackStats(this.webpackStats.values())\n    const path = join(this.distDir, 'server', WEBPACK_STATS)\n    deleteCache(path)\n    await writeFileAtomic(path, JSON.stringify(webpackStats, null, 2))\n  }\n\n  async loadBuildManifest(\n    pageName: string,\n    type: 'app' | 'pages' = 'pages'\n  ): Promise<void> {\n    this.buildManifests.set(\n      getEntryKey(type, 'server', pageName),\n      await readPartialManifest(this.distDir, BUILD_MANIFEST, pageName, type)\n    )\n  }\n\n  async loadWebpackStats(\n    pageName: string,\n    type: 'app' | 'pages' = 'pages'\n  ): Promise<void> {\n    this.webpackStats.set(\n      getEntryKey(type, 'client', pageName),\n      await readPartialManifest(this.distDir, WEBPACK_STATS, pageName, type)\n    )\n  }\n\n  private mergeWebpackStats(statsFiles: Iterable<WebpackStats>): WebpackStats {\n    const entrypoints: Record<string, StatsChunkGroup> = {}\n    const assets: Map<string, StatsAsset> = new Map()\n    const chunks: Map<string, StatsChunk> = new Map()\n    const modules: Map<string | number, StatsModule> = new Map()\n\n    for (const statsFile of statsFiles) {\n      if (statsFile.entrypoints) {\n        for (const [k, v] of Object.entries(statsFile.entrypoints)) {\n          if (!entrypoints[k]) {\n            entrypoints[k] = v\n          }\n        }\n      }\n\n      if (statsFile.assets) {\n        for (const asset of statsFile.assets) {\n          if (!assets.has(asset.name)) {\n            assets.set(asset.name, asset)\n          }\n        }\n      }\n\n      if (statsFile.chunks) {\n        for (const chunk of statsFile.chunks) {\n          if (!chunks.has(chunk.name)) {\n            chunks.set(chunk.name, chunk)\n          }\n        }\n      }\n\n      if (statsFile.modules) {\n        for (const module of statsFile.modules) {\n          const id = module.id\n          if (id != null) {\n            // Merge the chunk list for the module. This can vary across endpoints.\n            const existing = modules.get(id)\n            if (existing == null) {\n              modules.set(id, module)\n            } else if (module.chunks != null && existing.chunks != null) {\n              for (const chunk of module.chunks) {\n                if (!existing.chunks.includes(chunk)) {\n                  existing.chunks.push(chunk)\n                }\n              }\n            }\n          }\n        }\n      }\n    }\n\n    return {\n      entrypoints,\n      assets: [...assets.values()],\n      chunks: [...chunks.values()],\n      modules: [...modules.values()],\n    }\n  }\n\n  private mergeBuildManifests(manifests: Iterable<BuildManifest>) {\n    const manifest: Partial<BuildManifest> & Pick<BuildManifest, 'pages'> = {\n      pages: {\n        '/_app': [],\n      },\n      // Something in next.js depends on these to exist even for app dir rendering\n      devFiles: [],\n      ampDevFiles: [],\n      polyfillFiles: [],\n      lowPriorityFiles: [\n        `static/${this.buildId}/_ssgManifest.js`,\n        `static/${this.buildId}/_buildManifest.js`,\n      ],\n      rootMainFiles: [],\n      ampFirstPages: [],\n    }\n    for (const m of manifests) {\n      Object.assign(manifest.pages, m.pages)\n      if (m.rootMainFiles.length) manifest.rootMainFiles = m.rootMainFiles\n      // polyfillFiles should always be the same, so we can overwrite instead of actually merging\n      if (m.polyfillFiles.length) manifest.polyfillFiles = m.polyfillFiles\n    }\n    manifest.pages = sortObjectByKey(manifest.pages) as BuildManifest['pages']\n    return manifest\n  }\n\n  private async writeBuildManifest(\n    entrypoints: Entrypoints,\n    devRewrites: SetupOpts['fsChecker']['rewrites'] | undefined,\n    productionRewrites: CustomRoutes['rewrites'] | undefined\n  ): Promise<void> {\n    const rewrites = productionRewrites ?? {\n      ...devRewrites,\n      beforeFiles: (devRewrites?.beforeFiles ?? []).map(processRoute),\n      afterFiles: (devRewrites?.afterFiles ?? []).map(processRoute),\n      fallback: (devRewrites?.fallback ?? []).map(processRoute),\n    }\n    const buildManifest = this.mergeBuildManifests(this.buildManifests.values())\n    const buildManifestPath = join(this.distDir, BUILD_MANIFEST)\n    const middlewareBuildManifestPath = join(\n      this.distDir,\n      'server',\n      `${MIDDLEWARE_BUILD_MANIFEST}.js`\n    )\n    const interceptionRewriteManifestPath = join(\n      this.distDir,\n      'server',\n      `${INTERCEPTION_ROUTE_REWRITE_MANIFEST}.js`\n    )\n    deleteCache(buildManifestPath)\n    deleteCache(middlewareBuildManifestPath)\n    deleteCache(interceptionRewriteManifestPath)\n    await writeFileAtomic(\n      buildManifestPath,\n      JSON.stringify(buildManifest, null, 2)\n    )\n    await writeFileAtomic(\n      middlewareBuildManifestPath,\n      // we use globalThis here because middleware can be node\n      // which doesn't have \"self\"\n      createEdgeRuntimeManifest(buildManifest)\n    )\n\n    const interceptionRewrites = JSON.stringify(\n      rewrites.beforeFiles.filter(isInterceptionRouteRewrite)\n    )\n\n    await writeFileAtomic(\n      interceptionRewriteManifestPath,\n      `self.__INTERCEPTION_ROUTE_REWRITE_MANIFEST=${JSON.stringify(\n        interceptionRewrites\n      )};`\n    )\n\n    const pagesKeys = [...entrypoints.page.keys()]\n    if (entrypoints.global.app) {\n      pagesKeys.push('/_app')\n    }\n    if (entrypoints.global.error) {\n      pagesKeys.push('/_error')\n    }\n\n    const sortedPageKeys = getSortedRoutes(pagesKeys)\n    const content: ClientBuildManifest = {\n      __rewrites: normalizeRewritesForBuildManifest(rewrites) as any,\n      ...Object.fromEntries(\n        sortedPageKeys.map((pathname) => [\n          pathname,\n          [`static/chunks/pages${pathname === '/' ? '/index' : pathname}.js`],\n        ])\n      ),\n      sortedPages: sortedPageKeys,\n    }\n    const buildManifestJs = `self.__BUILD_MANIFEST = ${JSON.stringify(\n      content\n    )};self.__BUILD_MANIFEST_CB && self.__BUILD_MANIFEST_CB()`\n    await writeFileAtomic(\n      join(this.distDir, 'static', this.buildId, '_buildManifest.js'),\n      buildManifestJs\n    )\n    await writeFileAtomic(\n      join(this.distDir, 'static', this.buildId, '_ssgManifest.js'),\n      srcEmptySsgManifest\n    )\n  }\n\n  private async writeClientMiddlewareManifest(): Promise<void> {\n    const middlewareManifest = this.mergeMiddlewareManifests(\n      this.middlewareManifests.values()\n    )\n\n    const matchers = middlewareManifest?.middleware['/']?.matchers || []\n\n    const clientMiddlewareManifestPath = join(\n      this.distDir,\n      'static',\n      this.buildId,\n      `${TURBOPACK_CLIENT_MIDDLEWARE_MANIFEST}`\n    )\n    deleteCache(clientMiddlewareManifestPath)\n    await writeFileAtomic(\n      clientMiddlewareManifestPath,\n      JSON.stringify(matchers, null, 2)\n    )\n  }\n\n  private async writeFallbackBuildManifest(): Promise<void> {\n    const fallbackBuildManifest = this.mergeBuildManifests(\n      [\n        this.buildManifests.get(getEntryKey('pages', 'server', '_app')),\n        this.buildManifests.get(getEntryKey('pages', 'server', '_error')),\n      ].filter(Boolean) as BuildManifest[]\n    )\n    const fallbackBuildManifestPath = join(\n      this.distDir,\n      `fallback-${BUILD_MANIFEST}`\n    )\n    deleteCache(fallbackBuildManifestPath)\n    await writeFileAtomic(\n      fallbackBuildManifestPath,\n      JSON.stringify(fallbackBuildManifest, null, 2)\n    )\n  }\n\n  async loadFontManifest(\n    pageName: string,\n    type: 'app' | 'pages' = 'pages'\n  ): Promise<void> {\n    this.fontManifests.set(\n      getEntryKey(type, 'server', pageName),\n      await readPartialManifest(\n        this.distDir,\n        `${NEXT_FONT_MANIFEST}.json`,\n        pageName,\n        type\n      )\n    )\n  }\n\n  private mergeFontManifests(manifests: Iterable<NextFontManifest>) {\n    const manifest: NextFontManifest = {\n      app: {},\n      appUsingSizeAdjust: false,\n      pages: {},\n      pagesUsingSizeAdjust: false,\n    }\n    for (const m of manifests) {\n      Object.assign(manifest.app, m.app)\n      Object.assign(manifest.pages, m.pages)\n\n      manifest.appUsingSizeAdjust =\n        manifest.appUsingSizeAdjust || m.appUsingSizeAdjust\n      manifest.pagesUsingSizeAdjust =\n        manifest.pagesUsingSizeAdjust || m.pagesUsingSizeAdjust\n    }\n    manifest.app = sortObjectByKey(manifest.app)\n    manifest.pages = sortObjectByKey(manifest.pages)\n    return manifest\n  }\n\n  private async writeNextFontManifest(): Promise<void> {\n    const fontManifest = this.mergeFontManifests(this.fontManifests.values())\n    const json = JSON.stringify(fontManifest, null, 2)\n\n    const fontManifestJsonPath = join(\n      this.distDir,\n      'server',\n      `${NEXT_FONT_MANIFEST}.json`\n    )\n    const fontManifestJsPath = join(\n      this.distDir,\n      'server',\n      `${NEXT_FONT_MANIFEST}.js`\n    )\n    deleteCache(fontManifestJsonPath)\n    deleteCache(fontManifestJsPath)\n    await writeFileAtomic(fontManifestJsonPath, json)\n    await writeFileAtomic(\n      fontManifestJsPath,\n      `self.__NEXT_FONT_MANIFEST=${JSON.stringify(json)}`\n    )\n  }\n\n  /**\n   * @returns If the manifest was written or not\n   */\n  async loadMiddlewareManifest(\n    pageName: string,\n    type: 'pages' | 'app' | 'middleware' | 'instrumentation'\n  ): Promise<boolean> {\n    const middlewareManifestPath = getManifestPath(\n      pageName,\n      this.distDir,\n      MIDDLEWARE_MANIFEST,\n      type,\n      true\n    )\n\n    // middlewareManifest is actually \"edge manifest\" and not all routes are edge runtime. If it is not written we skip it.\n    if (!existsSync(middlewareManifestPath)) {\n      return false\n    }\n\n    this.middlewareManifests.set(\n      getEntryKey(\n        type === 'middleware' || type === 'instrumentation' ? 'root' : type,\n        'server',\n        pageName\n      ),\n      await readPartialManifest(\n        this.distDir,\n        MIDDLEWARE_MANIFEST,\n        pageName,\n        type\n      )\n    )\n\n    return true\n  }\n\n  getMiddlewareManifest(key: EntryKey) {\n    return this.middlewareManifests.get(key)\n  }\n\n  deleteMiddlewareManifest(key: EntryKey) {\n    return this.middlewareManifests.delete(key)\n  }\n\n  private mergeMiddlewareManifests(\n    manifests: Iterable<TurbopackMiddlewareManifest>\n  ): MiddlewareManifest {\n    const manifest: MiddlewareManifest = {\n      version: 3,\n      middleware: {},\n      sortedMiddleware: [],\n      functions: {},\n    }\n    let instrumentation: InstrumentationDefinition | undefined = undefined\n    for (const m of manifests) {\n      Object.assign(manifest.functions, m.functions)\n      Object.assign(manifest.middleware, m.middleware)\n      if (m.instrumentation) {\n        instrumentation = m.instrumentation\n      }\n    }\n    manifest.functions = sortObjectByKey(manifest.functions)\n    manifest.middleware = sortObjectByKey(manifest.middleware)\n    const updateFunctionDefinition = (\n      fun: EdgeFunctionDefinition\n    ): EdgeFunctionDefinition => {\n      return {\n        ...fun,\n        files: [...(instrumentation?.files ?? []), ...fun.files],\n      }\n    }\n    for (const key of Object.keys(manifest.middleware)) {\n      const value = manifest.middleware[key]\n      manifest.middleware[key] = updateFunctionDefinition(value)\n    }\n    for (const key of Object.keys(manifest.functions)) {\n      const value = manifest.functions[key]\n      manifest.functions[key] = updateFunctionDefinition(value)\n    }\n    for (const fun of Object.values(manifest.functions).concat(\n      Object.values(manifest.middleware)\n    )) {\n      for (const matcher of fun.matchers) {\n        if (!matcher.regexp) {\n          matcher.regexp = pathToRegexp(matcher.originalSource, [], {\n            delimiter: '/',\n            sensitive: false,\n            strict: true,\n          }).source.replaceAll('\\\\/', '/')\n        }\n      }\n    }\n    manifest.sortedMiddleware = Object.keys(manifest.middleware)\n\n    return manifest\n  }\n\n  private async writeMiddlewareManifest(): Promise<void> {\n    const middlewareManifest = this.mergeMiddlewareManifests(\n      this.middlewareManifests.values()\n    )\n\n    // Normalize regexes as it uses path-to-regexp\n    for (const key in middlewareManifest.middleware) {\n      middlewareManifest.middleware[key].matchers.forEach((matcher) => {\n        if (!matcher.regexp.startsWith('^')) {\n          const parsedPage = tryToParsePath(matcher.regexp)\n          if (parsedPage.error || !parsedPage.regexStr) {\n            throw new Error(`Invalid source: ${matcher.regexp}`)\n          }\n          matcher.regexp = parsedPage.regexStr\n        }\n      })\n    }\n\n    const middlewareManifestPath = join(\n      this.distDir,\n      'server',\n      MIDDLEWARE_MANIFEST\n    )\n    deleteCache(middlewareManifestPath)\n    await writeFileAtomic(\n      middlewareManifestPath,\n      JSON.stringify(middlewareManifest, null, 2)\n    )\n  }\n\n  async loadPagesManifest(pageName: string): Promise<void> {\n    this.pagesManifests.set(\n      getEntryKey('pages', 'server', pageName),\n      await readPartialManifest(this.distDir, PAGES_MANIFEST, pageName)\n    )\n  }\n\n  private mergePagesManifests(manifests: Iterable<PagesManifest>) {\n    const manifest: PagesManifest = {}\n    for (const m of manifests) {\n      Object.assign(manifest, m)\n    }\n    return sortObjectByKey(manifest)\n  }\n\n  private async writePagesManifest(): Promise<void> {\n    const pagesManifest = this.mergePagesManifests(this.pagesManifests.values())\n    const pagesManifestPath = join(this.distDir, 'server', PAGES_MANIFEST)\n    deleteCache(pagesManifestPath)\n    await writeFileAtomic(\n      pagesManifestPath,\n      JSON.stringify(pagesManifest, null, 2)\n    )\n  }\n\n  async writeManifests({\n    devRewrites,\n    productionRewrites,\n    entrypoints,\n  }: {\n    devRewrites: SetupOpts['fsChecker']['rewrites'] | undefined\n    productionRewrites: CustomRoutes['rewrites'] | undefined\n    entrypoints: Entrypoints\n  }) {\n    await this.writeActionManifest()\n    await this.writeAppBuildManifest()\n    await this.writeAppPathsManifest()\n    await this.writeBuildManifest(entrypoints, devRewrites, productionRewrites)\n    await this.writeFallbackBuildManifest()\n    await this.writeMiddlewareManifest()\n    await this.writeClientMiddlewareManifest()\n    await this.writeNextFontManifest()\n    await this.writePagesManifest()\n\n    if (process.env.TURBOPACK_STATS != null) {\n      await this.writeWebpackStats()\n    }\n  }\n}\n\nfunction sortObjectByKey(obj: Record<string, any>) {\n  return Object.keys(obj)\n    .sort()\n    .reduce(\n      (acc, key) => {\n        acc[key] = obj[key]\n        return acc\n      },\n      {} as Record<string, any>\n    )\n}\n"], "names": ["TurbopackManifestLoader", "getManifestPath", "page", "distDir", "name", "type", "firstCall", "manifestPath", "posix", "join", "getAssetPathFromRoute", "isSitemapRoute", "test", "existsSync", "replace", "endsWith", "metadataPage", "addRouteSuffix", "addMetadataIdToRoute", "removeRouteSuffix", "readPartialManifest", "pageName", "JSON", "parse", "readFile", "delete", "key", "actionManifests", "appBuildManifests", "appPathsManifests", "buildManifests", "fontManifests", "middlewareManifests", "pagesManifests", "webpackStats", "loadActionManifest", "set", "getEntry<PERSON>ey", "SERVER_REFERENCE_MANIFEST", "mergeActionManifests", "manifests", "manifest", "node", "edge", "<PERSON><PERSON><PERSON>", "mergeActionIds", "actionEntries", "other", "action", "workers", "layer", "Object", "assign", "m", "entry", "sortObjectByKey", "writeActionManifest", "actionManifest", "values", "actionManifestJsonPath", "actionManifestJsPath", "json", "stringify", "deleteCache", "writeFileAtomic", "loadAppBuildManifest", "APP_BUILD_MANIFEST", "mergeAppBuildManifests", "pages", "writeAppBuildManifest", "appBuildManifest", "appBuildManifestPath", "loadAppPathsManifest", "APP_PATHS_MANIFEST", "writeAppPathsManifest", "appPathsManifest", "mergePagesManifests", "appPathsManifestPath", "writeWebpackStats", "mergeWebpackStats", "path", "WEBPACK_STATS", "loadBuildManifest", "BUILD_MANIFEST", "loadWebpackStats", "statsFiles", "entrypoints", "assets", "Map", "chunks", "modules", "statsFile", "k", "v", "entries", "asset", "has", "chunk", "module", "id", "existing", "get", "includes", "push", "mergeBuildManifests", "devFiles", "ampDevFiles", "polyfillFiles", "lowPriorityFiles", "buildId", "rootMainFiles", "ampFirstPages", "length", "writeBuildManifest", "devRewrites", "productionRewrites", "rewrites", "beforeFiles", "map", "processRoute", "afterFiles", "fallback", "buildManifest", "buildManifestPath", "middlewareBuildManifestPath", "MIDDLEWARE_BUILD_MANIFEST", "interceptionRewriteManifestPath", "INTERCEPTION_ROUTE_REWRITE_MANIFEST", "createEdgeRuntimeManifest", "interceptionRewrites", "filter", "isInterceptionRouteRewrite", "pagesKeys", "keys", "global", "app", "error", "sortedPageKeys", "getSortedRoutes", "content", "__rewrites", "normalizeRewritesForBuildManifest", "fromEntries", "pathname", "sortedPages", "buildManifestJs", "srcEmptySsgManifest", "writeClientMiddlewareManifest", "middlewareManifest", "mergeMiddlewareManifests", "matchers", "middleware", "clientMiddlewareManifestPath", "TURBOPACK_CLIENT_MIDDLEWARE_MANIFEST", "writeFallbackBuildManifest", "fallbackBuildManifest", "Boolean", "fallbackBuildManifestPath", "loadFontManifest", "NEXT_FONT_MANIFEST", "mergeFontManifests", "appUsingSizeAdjust", "pagesUsingSizeAdjust", "writeNextFontManifest", "fontManifest", "fontManifestJsonPath", "fontManifestJsPath", "loadMiddlewareManifest", "middlewareManifestPath", "MIDDLEWARE_MANIFEST", "getMiddlewareManifest", "deleteMiddlewareManifest", "version", "sortedMiddleware", "functions", "instrumentation", "undefined", "updateFunctionDefinition", "fun", "files", "value", "concat", "matcher", "regexp", "pathToRegexp", "originalSource", "delimiter", "sensitive", "strict", "source", "replaceAll", "writeMiddlewareManifest", "for<PERSON>ach", "startsWith", "parsedPage", "tryToParsePath", "regexStr", "Error", "loadPagesManifest", "PAGES_MANIFEST", "writePagesManifest", "pagesManifest", "pagesManifestPath", "writeManifests", "process", "env", "TURBOPACK_STATS", "constructor", "obj", "sort", "reduce", "acc"], "mappings": ";;;;+BAoIaA;;;eAAAA;;;;8BAtHgB;2BAgBtB;sBACqB;0BACH;8BAEG;6BACI;oDACW;qCAOpC;gFAC2B;0BACS;uBAEX;oBACL;gCAKpB;gCACwB;AAuB/B,MAAMC,kBAAkB,CACtBC,MACAC,SACAC,MACAC,MACAC;IAEA,IAAIC,eAAeC,WAAK,CAACC,IAAI,CAC3BN,SACC,UACDE,MACAA,SAAS,gBAAgBA,SAAS,oBAC9B,KACAA,SAAS,QACPH,OACAQ,IAAAA,8BAAqB,EAACR,OAC5BE;IAGF,IAAIE,WAAW;QACb,MAAMK,iBAAiB,8BAA8BC,IAAI,CAACV;QAC1D,mDAAmD;QACnD,IAAIS,kBAAkB,CAACE,IAAAA,cAAU,EAACN,eAAe;YAC/CA,eAAeN,gBACbC,KAAKY,OAAO,CAAC,qBAAqB,uBAClCX,SACAC,MACAC,MACA;QAEJ;QACA,oDAAoD;QACpD,IAAI,CAACQ,IAAAA,cAAU,EAACN,iBAAiBL,KAAKa,QAAQ,CAAC,WAAW;YACxD,6IAA6I;YAC7I,IAAIC,eAAeC,IAAAA,8BAAc,EAC/BC,IAAAA,oCAAoB,EAACC,IAAAA,iCAAiB,EAACjB;YAEzCK,eAAeN,gBAAgBe,cAAcb,SAASC,MAAMC,MAAM;QACpE;IACF;IAEA,OAAOE;AACT;AAEA,eAAea,oBACbjB,OAAe,EACfC,IAAkB,EAClBiB,QAAgB,EAChBhB,IAAkE;IAAlEA,IAAAA,iBAAAA,OAA2D;IAE3D,MAAMH,OAAOmB;IACb,MAAMd,eAAeN,gBAAgBC,MAAMC,SAASC,MAAMC,MAAM;IAChE,OAAOiB,KAAKC,KAAK,CAAC,MAAMC,IAAAA,kBAAQ,EAAChB,WAAK,CAACC,IAAI,CAACF,eAAe;AAC7D;AAEO,MAAMP;IA6BXyB,OAAOC,GAAa,EAAE;QACpB,IAAI,CAACC,eAAe,CAACF,MAAM,CAACC;QAC5B,IAAI,CAACE,iBAAiB,CAACH,MAAM,CAACC;QAC9B,IAAI,CAACG,iBAAiB,CAACJ,MAAM,CAACC;QAC9B,IAAI,CAACI,cAAc,CAACL,MAAM,CAACC;QAC3B,IAAI,CAACK,aAAa,CAACN,MAAM,CAACC;QAC1B,IAAI,CAACM,mBAAmB,CAACP,MAAM,CAACC;QAChC,IAAI,CAACO,cAAc,CAACR,MAAM,CAACC;QAC3B,IAAI,CAACQ,YAAY,CAACT,MAAM,CAACC;IAC3B;IAEA,MAAMS,mBAAmBd,QAAgB,EAAiB;QACxD,IAAI,CAACM,eAAe,CAACS,GAAG,CACtBC,IAAAA,qBAAW,EAAC,OAAO,UAAUhB,WAC7B,MAAMD,oBACJ,IAAI,CAACjB,OAAO,EACZ,AAAC,KAAEmC,oCAAyB,GAAC,SAC7BjB,UACA;IAGN;IAEA,MAAckB,qBAAqBC,SAAmC,EAAE;QAEtE,MAAMC,WAA2B;YAC/BC,MAAM,CAAC;YACPC,MAAM,CAAC;YACPC,eAAe,IAAI,CAACA,aAAa;QACnC;QAEA,SAASC,eACPC,aAA4B,EAC5BC,KAAoB;YAEpB,IAAK,MAAMrB,OAAOqB,MAAO;oBACPD,gBAAcpB;;gBAA9B,MAAMsB,SAAUF,MAAAA,iBAAAA,cAAa,CAACpB,OAAAA,IAAI,gBAAlBoB,cAAa,CAACpB,KAAI,GAAK;oBACrCuB,SAAS,CAAC;oBACVC,OAAO,CAAC;gBACV;gBACAC,OAAOC,MAAM,CAACJ,OAAOC,OAAO,EAAEF,KAAK,CAACrB,IAAI,CAACuB,OAAO;gBAChDE,OAAOC,MAAM,CAACJ,OAAOE,KAAK,EAAEH,KAAK,CAACrB,IAAI,CAACwB,KAAK;YAC9C;QACF;QAEA,KAAK,MAAMG,KAAKb,UAAW;YACzBK,eAAeJ,SAASC,IAAI,EAAEW,EAAEX,IAAI;YACpCG,eAAeJ,SAASE,IAAI,EAAEU,EAAEV,IAAI;QACtC;QACA,IAAK,MAAMjB,OAAOe,SAASC,IAAI,CAAE;YAC/B,MAAMY,QAAQb,SAASC,IAAI,CAAChB,IAAI;YAChC4B,MAAML,OAAO,GAAGM,gBAAgBD,MAAML,OAAO;YAC7CK,MAAMJ,KAAK,GAAGK,gBAAgBD,MAAMJ,KAAK;QAC3C;QACA,IAAK,MAAMxB,OAAOe,SAASE,IAAI,CAAE;YAC/B,MAAMW,QAAQb,SAASE,IAAI,CAACjB,IAAI;YAChC4B,MAAML,OAAO,GAAGM,gBAAgBD,MAAML,OAAO;YAC7CK,MAAMJ,KAAK,GAAGK,gBAAgBD,MAAMJ,KAAK;QAC3C;QAEA,OAAOT;IACT;IAEA,MAAce,sBAAqC;QACjD,MAAMC,iBAAiB,MAAM,IAAI,CAAClB,oBAAoB,CACpD,IAAI,CAACZ,eAAe,CAAC+B,MAAM;QAE7B,MAAMC,yBAAyBlD,IAAAA,UAAI,EACjC,IAAI,CAACN,OAAO,EACZ,UACA,AAAC,KAAEmC,oCAAyB,GAAC;QAE/B,MAAMsB,uBAAuBnD,IAAAA,UAAI,EAC/B,IAAI,CAACN,OAAO,EACZ,UACA,AAAC,KAAEmC,oCAAyB,GAAC;QAE/B,MAAMuB,OAAOvC,KAAKwC,SAAS,CAACL,gBAAgB,MAAM;QAClDM,IAAAA,yBAAW,EAACJ;QACZI,IAAAA,yBAAW,EAACH;QACZ,MAAMI,IAAAA,4BAAe,EAACL,wBAAwBE;QAC9C,MAAMG,IAAAA,4BAAe,EACnBJ,sBACA,AAAC,gCAA6BtC,KAAKwC,SAAS,CAACD;IAEjD;IAEA,MAAMI,qBAAqB5C,QAAgB,EAAiB;QAC1D,IAAI,CAACO,iBAAiB,CAACQ,GAAG,CACxBC,IAAAA,qBAAW,EAAC,OAAO,UAAUhB,WAC7B,MAAMD,oBACJ,IAAI,CAACjB,OAAO,EACZ+D,6BAAkB,EAClB7C,UACA;IAGN;IAEQ8C,uBAAuB3B,SAAqC,EAAE;QACpE,MAAMC,WAA6B;YACjC2B,OAAO,CAAC;QACV;QACA,KAAK,MAAMf,KAAKb,UAAW;YACzBW,OAAOC,MAAM,CAACX,SAAS2B,KAAK,EAAEf,EAAEe,KAAK;QACvC;QACA3B,SAAS2B,KAAK,GAAGb,gBAAgBd,SAAS2B,KAAK;QAC/C,OAAO3B;IACT;IAEA,MAAc4B,wBAAuC;QACnD,MAAMC,mBAAmB,IAAI,CAACH,sBAAsB,CAClD,IAAI,CAACvC,iBAAiB,CAAC8B,MAAM;QAE/B,MAAMa,uBAAuB9D,IAAAA,UAAI,EAAC,IAAI,CAACN,OAAO,EAAE+D,6BAAkB;QAClEH,IAAAA,yBAAW,EAACQ;QACZ,MAAMP,IAAAA,4BAAe,EACnBO,sBACAjD,KAAKwC,SAAS,CAACQ,kBAAkB,MAAM;IAE3C;IAEA,MAAME,qBAAqBnD,QAAgB,EAAiB;QAC1D,IAAI,CAACQ,iBAAiB,CAACO,GAAG,CACxBC,IAAAA,qBAAW,EAAC,OAAO,UAAUhB,WAC7B,MAAMD,oBACJ,IAAI,CAACjB,OAAO,EACZsE,6BAAkB,EAClBpD,UACA;IAGN;IAEA,MAAcqD,wBAAuC;QACnD,MAAMC,mBAAmB,IAAI,CAACC,mBAAmB,CAC/C,IAAI,CAAC/C,iBAAiB,CAAC6B,MAAM;QAE/B,MAAMmB,uBAAuBpE,IAAAA,UAAI,EAC/B,IAAI,CAACN,OAAO,EACZ,UACAsE,6BAAkB;QAEpBV,IAAAA,yBAAW,EAACc;QACZ,MAAMb,IAAAA,4BAAe,EACnBa,sBACAvD,KAAKwC,SAAS,CAACa,kBAAkB,MAAM;IAE3C;IAEA,MAAcG,oBAAmC;QAC/C,MAAM5C,eAAe,IAAI,CAAC6C,iBAAiB,CAAC,IAAI,CAAC7C,YAAY,CAACwB,MAAM;QACpE,MAAMsB,OAAOvE,IAAAA,UAAI,EAAC,IAAI,CAACN,OAAO,EAAE,UAAU8E,wBAAa;QACvDlB,IAAAA,yBAAW,EAACiB;QACZ,MAAMhB,IAAAA,4BAAe,EAACgB,MAAM1D,KAAKwC,SAAS,CAAC5B,cAAc,MAAM;IACjE;IAEA,MAAMgD,kBACJ7D,QAAgB,EAChBhB,IAA+B,EAChB;QADfA,IAAAA,iBAAAA,OAAwB;QAExB,IAAI,CAACyB,cAAc,CAACM,GAAG,CACrBC,IAAAA,qBAAW,EAAChC,MAAM,UAAUgB,WAC5B,MAAMD,oBAAoB,IAAI,CAACjB,OAAO,EAAEgF,yBAAc,EAAE9D,UAAUhB;IAEtE;IAEA,MAAM+E,iBACJ/D,QAAgB,EAChBhB,IAA+B,EAChB;QADfA,IAAAA,iBAAAA,OAAwB;QAExB,IAAI,CAAC6B,YAAY,CAACE,GAAG,CACnBC,IAAAA,qBAAW,EAAChC,MAAM,UAAUgB,WAC5B,MAAMD,oBAAoB,IAAI,CAACjB,OAAO,EAAE8E,wBAAa,EAAE5D,UAAUhB;IAErE;IAEQ0E,kBAAkBM,UAAkC,EAAgB;QAC1E,MAAMC,cAA+C,CAAC;QACtD,MAAMC,SAAkC,IAAIC;QAC5C,MAAMC,SAAkC,IAAID;QAC5C,MAAME,UAA6C,IAAIF;QAEvD,KAAK,MAAMG,aAAaN,WAAY;YAClC,IAAIM,UAAUL,WAAW,EAAE;gBACzB,KAAK,MAAM,CAACM,GAAGC,EAAE,IAAI1C,OAAO2C,OAAO,CAACH,UAAUL,WAAW,EAAG;oBAC1D,IAAI,CAACA,WAAW,CAACM,EAAE,EAAE;wBACnBN,WAAW,CAACM,EAAE,GAAGC;oBACnB;gBACF;YACF;YAEA,IAAIF,UAAUJ,MAAM,EAAE;gBACpB,KAAK,MAAMQ,SAASJ,UAAUJ,MAAM,CAAE;oBACpC,IAAI,CAACA,OAAOS,GAAG,CAACD,MAAM3F,IAAI,GAAG;wBAC3BmF,OAAOnD,GAAG,CAAC2D,MAAM3F,IAAI,EAAE2F;oBACzB;gBACF;YACF;YAEA,IAAIJ,UAAUF,MAAM,EAAE;gBACpB,KAAK,MAAMQ,SAASN,UAAUF,MAAM,CAAE;oBACpC,IAAI,CAACA,OAAOO,GAAG,CAACC,MAAM7F,IAAI,GAAG;wBAC3BqF,OAAOrD,GAAG,CAAC6D,MAAM7F,IAAI,EAAE6F;oBACzB;gBACF;YACF;YAEA,IAAIN,UAAUD,OAAO,EAAE;gBACrB,KAAK,MAAMQ,UAAUP,UAAUD,OAAO,CAAE;oBACtC,MAAMS,KAAKD,OAAOC,EAAE;oBACpB,IAAIA,MAAM,MAAM;wBACd,uEAAuE;wBACvE,MAAMC,WAAWV,QAAQW,GAAG,CAACF;wBAC7B,IAAIC,YAAY,MAAM;4BACpBV,QAAQtD,GAAG,CAAC+D,IAAID;wBAClB,OAAO,IAAIA,OAAOT,MAAM,IAAI,QAAQW,SAASX,MAAM,IAAI,MAAM;4BAC3D,KAAK,MAAMQ,SAASC,OAAOT,MAAM,CAAE;gCACjC,IAAI,CAACW,SAASX,MAAM,CAACa,QAAQ,CAACL,QAAQ;oCACpCG,SAASX,MAAM,CAACc,IAAI,CAACN;gCACvB;4BACF;wBACF;oBACF;gBACF;YACF;QACF;QAEA,OAAO;YACLX;YACAC,QAAQ;mBAAIA,OAAO7B,MAAM;aAAG;YAC5B+B,QAAQ;mBAAIA,OAAO/B,MAAM;aAAG;YAC5BgC,SAAS;mBAAIA,QAAQhC,MAAM;aAAG;QAChC;IACF;IAEQ8C,oBAAoBhE,SAAkC,EAAE;QAC9D,MAAMC,WAAkE;YACtE2B,OAAO;gBACL,SAAS,EAAE;YACb;YACA,4EAA4E;YAC5EqC,UAAU,EAAE;YACZC,aAAa,EAAE;YACfC,eAAe,EAAE;YACjBC,kBAAkB;gBACf,YAAS,IAAI,CAACC,OAAO,GAAC;gBACtB,YAAS,IAAI,CAACA,OAAO,GAAC;aACxB;YACDC,eAAe,EAAE;YACjBC,eAAe,EAAE;QACnB;QACA,KAAK,MAAM1D,KAAKb,UAAW;YACzBW,OAAOC,MAAM,CAACX,SAAS2B,KAAK,EAAEf,EAAEe,KAAK;YACrC,IAAIf,EAAEyD,aAAa,CAACE,MAAM,EAAEvE,SAASqE,aAAa,GAAGzD,EAAEyD,aAAa;YACpE,2FAA2F;YAC3F,IAAIzD,EAAEsD,aAAa,CAACK,MAAM,EAAEvE,SAASkE,aAAa,GAAGtD,EAAEsD,aAAa;QACtE;QACAlE,SAAS2B,KAAK,GAAGb,gBAAgBd,SAAS2B,KAAK;QAC/C,OAAO3B;IACT;IAEA,MAAcwE,mBACZ3B,WAAwB,EACxB4B,WAA2D,EAC3DC,kBAAwD,EACzC;YAGCD,0BACDA,yBACFA;QAJb,MAAME,WAAWD,6BAAAA,qBAAsB;YACrC,GAAGD,WAAW;YACdG,aAAa,AAACH,CAAAA,CAAAA,2BAAAA,+BAAAA,YAAaG,WAAW,YAAxBH,2BAA4B,EAAE,AAAD,EAAGI,GAAG,CAACC,iCAAY;YAC9DC,YAAY,AAACN,CAAAA,CAAAA,0BAAAA,+BAAAA,YAAaM,UAAU,YAAvBN,0BAA2B,EAAE,AAAD,EAAGI,GAAG,CAACC,iCAAY;YAC5DE,UAAU,AAACP,CAAAA,CAAAA,wBAAAA,+BAAAA,YAAaO,QAAQ,YAArBP,wBAAyB,EAAE,AAAD,EAAGI,GAAG,CAACC,iCAAY;QAC1D;QACA,MAAMG,gBAAgB,IAAI,CAAClB,mBAAmB,CAAC,IAAI,CAAC1E,cAAc,CAAC4B,MAAM;QACzE,MAAMiE,oBAAoBlH,IAAAA,UAAI,EAAC,IAAI,CAACN,OAAO,EAAEgF,yBAAc;QAC3D,MAAMyC,8BAA8BnH,IAAAA,UAAI,EACtC,IAAI,CAACN,OAAO,EACZ,UACA,AAAC,KAAE0H,oCAAyB,GAAC;QAE/B,MAAMC,kCAAkCrH,IAAAA,UAAI,EAC1C,IAAI,CAACN,OAAO,EACZ,UACA,AAAC,KAAE4H,8CAAmC,GAAC;QAEzChE,IAAAA,yBAAW,EAAC4D;QACZ5D,IAAAA,yBAAW,EAAC6D;QACZ7D,IAAAA,yBAAW,EAAC+D;QACZ,MAAM9D,IAAAA,4BAAe,EACnB2D,mBACArG,KAAKwC,SAAS,CAAC4D,eAAe,MAAM;QAEtC,MAAM1D,IAAAA,4BAAe,EACnB4D,6BACA,wDAAwD;QACxD,4BAA4B;QAC5BI,IAAAA,8CAAyB,EAACN;QAG5B,MAAMO,uBAAuB3G,KAAKwC,SAAS,CACzCsD,SAASC,WAAW,CAACa,MAAM,CAACC,8DAA0B;QAGxD,MAAMnE,IAAAA,4BAAe,EACnB8D,iCACA,AAAC,gDAA6CxG,KAAKwC,SAAS,CAC1DmE,wBACA;QAGJ,MAAMG,YAAY;eAAI9C,YAAYpF,IAAI,CAACmI,IAAI;SAAG;QAC9C,IAAI/C,YAAYgD,MAAM,CAACC,GAAG,EAAE;YAC1BH,UAAU7B,IAAI,CAAC;QACjB;QACA,IAAIjB,YAAYgD,MAAM,CAACE,KAAK,EAAE;YAC5BJ,UAAU7B,IAAI,CAAC;QACjB;QAEA,MAAMkC,iBAAiBC,IAAAA,sBAAe,EAACN;QACvC,MAAMO,UAA+B;YACnCC,YAAYC,IAAAA,sDAAiC,EAACzB;YAC9C,GAAGjE,OAAO2F,WAAW,CACnBL,eAAenB,GAAG,CAAC,CAACyB,WAAa;oBAC/BA;oBACA;wBAAE,wBAAqBA,CAAAA,aAAa,MAAM,WAAWA,QAAO,IAAE;qBAAK;iBACpE,EACF;YACDC,aAAaP;QACf;QACA,MAAMQ,kBAAkB,AAAC,6BAA0B3H,KAAKwC,SAAS,CAC/D6E,WACA;QACF,MAAM3E,IAAAA,4BAAe,EACnBvD,IAAAA,UAAI,EAAC,IAAI,CAACN,OAAO,EAAE,UAAU,IAAI,CAAC0G,OAAO,EAAE,sBAC3CoC;QAEF,MAAMjF,IAAAA,4BAAe,EACnBvD,IAAAA,UAAI,EAAC,IAAI,CAACN,OAAO,EAAE,UAAU,IAAI,CAAC0G,OAAO,EAAE,oBAC3CqC,wCAAmB;IAEvB;IAEA,MAAcC,gCAA+C;YAK1CC;QAJjB,MAAMA,qBAAqB,IAAI,CAACC,wBAAwB,CACtD,IAAI,CAACrH,mBAAmB,CAAC0B,MAAM;QAGjC,MAAM4F,WAAWF,CAAAA,uCAAAA,kCAAAA,mBAAoBG,UAAU,CAAC,IAAI,qBAAnCH,gCAAqCE,QAAQ,KAAI,EAAE;QAEpE,MAAME,+BAA+B/I,IAAAA,UAAI,EACvC,IAAI,CAACN,OAAO,EACZ,UACA,IAAI,CAAC0G,OAAO,EACZ,AAAC,KAAE4C,+CAAoC;QAEzC1F,IAAAA,yBAAW,EAACyF;QACZ,MAAMxF,IAAAA,4BAAe,EACnBwF,8BACAlI,KAAKwC,SAAS,CAACwF,UAAU,MAAM;IAEnC;IAEA,MAAcI,6BAA4C;QACxD,MAAMC,wBAAwB,IAAI,CAACnD,mBAAmB,CACpD;YACE,IAAI,CAAC1E,cAAc,CAACuE,GAAG,CAAChE,IAAAA,qBAAW,EAAC,SAAS,UAAU;YACvD,IAAI,CAACP,cAAc,CAACuE,GAAG,CAAChE,IAAAA,qBAAW,EAAC,SAAS,UAAU;SACxD,CAAC6F,MAAM,CAAC0B;QAEX,MAAMC,4BAA4BpJ,IAAAA,UAAI,EACpC,IAAI,CAACN,OAAO,EACZ,AAAC,cAAWgF,yBAAc;QAE5BpB,IAAAA,yBAAW,EAAC8F;QACZ,MAAM7F,IAAAA,4BAAe,EACnB6F,2BACAvI,KAAKwC,SAAS,CAAC6F,uBAAuB,MAAM;IAEhD;IAEA,MAAMG,iBACJzI,QAAgB,EAChBhB,IAA+B,EAChB;QADfA,IAAAA,iBAAAA,OAAwB;QAExB,IAAI,CAAC0B,aAAa,CAACK,GAAG,CACpBC,IAAAA,qBAAW,EAAChC,MAAM,UAAUgB,WAC5B,MAAMD,oBACJ,IAAI,CAACjB,OAAO,EACZ,AAAC,KAAE4J,6BAAkB,GAAC,SACtB1I,UACAhB;IAGN;IAEQ2J,mBAAmBxH,SAAqC,EAAE;QAChE,MAAMC,WAA6B;YACjC8F,KAAK,CAAC;YACN0B,oBAAoB;YACpB7F,OAAO,CAAC;YACR8F,sBAAsB;QACxB;QACA,KAAK,MAAM7G,KAAKb,UAAW;YACzBW,OAAOC,MAAM,CAACX,SAAS8F,GAAG,EAAElF,EAAEkF,GAAG;YACjCpF,OAAOC,MAAM,CAACX,SAAS2B,KAAK,EAAEf,EAAEe,KAAK;YAErC3B,SAASwH,kBAAkB,GACzBxH,SAASwH,kBAAkB,IAAI5G,EAAE4G,kBAAkB;YACrDxH,SAASyH,oBAAoB,GAC3BzH,SAASyH,oBAAoB,IAAI7G,EAAE6G,oBAAoB;QAC3D;QACAzH,SAAS8F,GAAG,GAAGhF,gBAAgBd,SAAS8F,GAAG;QAC3C9F,SAAS2B,KAAK,GAAGb,gBAAgBd,SAAS2B,KAAK;QAC/C,OAAO3B;IACT;IAEA,MAAc0H,wBAAuC;QACnD,MAAMC,eAAe,IAAI,CAACJ,kBAAkB,CAAC,IAAI,CAACjI,aAAa,CAAC2B,MAAM;QACtE,MAAMG,OAAOvC,KAAKwC,SAAS,CAACsG,cAAc,MAAM;QAEhD,MAAMC,uBAAuB5J,IAAAA,UAAI,EAC/B,IAAI,CAACN,OAAO,EACZ,UACA,AAAC,KAAE4J,6BAAkB,GAAC;QAExB,MAAMO,qBAAqB7J,IAAAA,UAAI,EAC7B,IAAI,CAACN,OAAO,EACZ,UACA,AAAC,KAAE4J,6BAAkB,GAAC;QAExBhG,IAAAA,yBAAW,EAACsG;QACZtG,IAAAA,yBAAW,EAACuG;QACZ,MAAMtG,IAAAA,4BAAe,EAACqG,sBAAsBxG;QAC5C,MAAMG,IAAAA,4BAAe,EACnBsG,oBACA,AAAC,+BAA4BhJ,KAAKwC,SAAS,CAACD;IAEhD;IAEA;;GAEC,GACD,MAAM0G,uBACJlJ,QAAgB,EAChBhB,IAAwD,EACtC;QAClB,MAAMmK,yBAAyBvK,gBAC7BoB,UACA,IAAI,CAAClB,OAAO,EACZsK,8BAAmB,EACnBpK,MACA;QAGF,uHAAuH;QACvH,IAAI,CAACQ,IAAAA,cAAU,EAAC2J,yBAAyB;YACvC,OAAO;QACT;QAEA,IAAI,CAACxI,mBAAmB,CAACI,GAAG,CAC1BC,IAAAA,qBAAW,EACThC,SAAS,gBAAgBA,SAAS,oBAAoB,SAASA,MAC/D,UACAgB,WAEF,MAAMD,oBACJ,IAAI,CAACjB,OAAO,EACZsK,8BAAmB,EACnBpJ,UACAhB;QAIJ,OAAO;IACT;IAEAqK,sBAAsBhJ,GAAa,EAAE;QACnC,OAAO,IAAI,CAACM,mBAAmB,CAACqE,GAAG,CAAC3E;IACtC;IAEAiJ,yBAAyBjJ,GAAa,EAAE;QACtC,OAAO,IAAI,CAACM,mBAAmB,CAACP,MAAM,CAACC;IACzC;IAEQ2H,yBACN7G,SAAgD,EAC5B;QACpB,MAAMC,WAA+B;YACnCmI,SAAS;YACTrB,YAAY,CAAC;YACbsB,kBAAkB,EAAE;YACpBC,WAAW,CAAC;QACd;QACA,IAAIC,kBAAyDC;QAC7D,KAAK,MAAM3H,KAAKb,UAAW;YACzBW,OAAOC,MAAM,CAACX,SAASqI,SAAS,EAAEzH,EAAEyH,SAAS;YAC7C3H,OAAOC,MAAM,CAACX,SAAS8G,UAAU,EAAElG,EAAEkG,UAAU;YAC/C,IAAIlG,EAAE0H,eAAe,EAAE;gBACrBA,kBAAkB1H,EAAE0H,eAAe;YACrC;QACF;QACAtI,SAASqI,SAAS,GAAGvH,gBAAgBd,SAASqI,SAAS;QACvDrI,SAAS8G,UAAU,GAAGhG,gBAAgBd,SAAS8G,UAAU;QACzD,MAAM0B,2BAA2B,CAC/BC;gBAIcH;YAFd,OAAO;gBACL,GAAGG,GAAG;gBACNC,OAAO;uBAAKJ,CAAAA,yBAAAA,mCAAAA,gBAAiBI,KAAK,YAAtBJ,yBAA0B,EAAE;uBAAMG,IAAIC,KAAK;iBAAC;YAC1D;QACF;QACA,KAAK,MAAMzJ,OAAOyB,OAAOkF,IAAI,CAAC5F,SAAS8G,UAAU,EAAG;YAClD,MAAM6B,QAAQ3I,SAAS8G,UAAU,CAAC7H,IAAI;YACtCe,SAAS8G,UAAU,CAAC7H,IAAI,GAAGuJ,yBAAyBG;QACtD;QACA,KAAK,MAAM1J,OAAOyB,OAAOkF,IAAI,CAAC5F,SAASqI,SAAS,EAAG;YACjD,MAAMM,QAAQ3I,SAASqI,SAAS,CAACpJ,IAAI;YACrCe,SAASqI,SAAS,CAACpJ,IAAI,GAAGuJ,yBAAyBG;QACrD;QACA,KAAK,MAAMF,OAAO/H,OAAOO,MAAM,CAACjB,SAASqI,SAAS,EAAEO,MAAM,CACxDlI,OAAOO,MAAM,CAACjB,SAAS8G,UAAU,GAChC;YACD,KAAK,MAAM+B,WAAWJ,IAAI5B,QAAQ,CAAE;gBAClC,IAAI,CAACgC,QAAQC,MAAM,EAAE;oBACnBD,QAAQC,MAAM,GAAGC,IAAAA,0BAAY,EAACF,QAAQG,cAAc,EAAE,EAAE,EAAE;wBACxDC,WAAW;wBACXC,WAAW;wBACXC,QAAQ;oBACV,GAAGC,MAAM,CAACC,UAAU,CAAC,OAAO;gBAC9B;YACF;QACF;QACArJ,SAASoI,gBAAgB,GAAG1H,OAAOkF,IAAI,CAAC5F,SAAS8G,UAAU;QAE3D,OAAO9G;IACT;IAEA,MAAcsJ,0BAAyC;QACrD,MAAM3C,qBAAqB,IAAI,CAACC,wBAAwB,CACtD,IAAI,CAACrH,mBAAmB,CAAC0B,MAAM;QAGjC,8CAA8C;QAC9C,IAAK,MAAMhC,OAAO0H,mBAAmBG,UAAU,CAAE;YAC/CH,mBAAmBG,UAAU,CAAC7H,IAAI,CAAC4H,QAAQ,CAAC0C,OAAO,CAAC,CAACV;gBACnD,IAAI,CAACA,QAAQC,MAAM,CAACU,UAAU,CAAC,MAAM;oBACnC,MAAMC,aAAaC,IAAAA,8BAAc,EAACb,QAAQC,MAAM;oBAChD,IAAIW,WAAW1D,KAAK,IAAI,CAAC0D,WAAWE,QAAQ,EAAE;wBAC5C,MAAM,qBAA8C,CAA9C,IAAIC,MAAM,AAAC,qBAAkBf,QAAQC,MAAM,GAA3C,qBAAA;mCAAA;wCAAA;0CAAA;wBAA6C;oBACrD;oBACAD,QAAQC,MAAM,GAAGW,WAAWE,QAAQ;gBACtC;YACF;QACF;QAEA,MAAM5B,yBAAyB/J,IAAAA,UAAI,EACjC,IAAI,CAACN,OAAO,EACZ,UACAsK,8BAAmB;QAErB1G,IAAAA,yBAAW,EAACyG;QACZ,MAAMxG,IAAAA,4BAAe,EACnBwG,wBACAlJ,KAAKwC,SAAS,CAACsF,oBAAoB,MAAM;IAE7C;IAEA,MAAMkD,kBAAkBjL,QAAgB,EAAiB;QACvD,IAAI,CAACY,cAAc,CAACG,GAAG,CACrBC,IAAAA,qBAAW,EAAC,SAAS,UAAUhB,WAC/B,MAAMD,oBAAoB,IAAI,CAACjB,OAAO,EAAEoM,yBAAc,EAAElL;IAE5D;IAEQuD,oBAAoBpC,SAAkC,EAAE;QAC9D,MAAMC,WAA0B,CAAC;QACjC,KAAK,MAAMY,KAAKb,UAAW;YACzBW,OAAOC,MAAM,CAACX,UAAUY;QAC1B;QACA,OAAOE,gBAAgBd;IACzB;IAEA,MAAc+J,qBAAoC;QAChD,MAAMC,gBAAgB,IAAI,CAAC7H,mBAAmB,CAAC,IAAI,CAAC3C,cAAc,CAACyB,MAAM;QACzE,MAAMgJ,oBAAoBjM,IAAAA,UAAI,EAAC,IAAI,CAACN,OAAO,EAAE,UAAUoM,yBAAc;QACrExI,IAAAA,yBAAW,EAAC2I;QACZ,MAAM1I,IAAAA,4BAAe,EACnB0I,mBACApL,KAAKwC,SAAS,CAAC2I,eAAe,MAAM;IAExC;IAEA,MAAME,eAAe,KAQpB,EAAE;QARkB,IAAA,EACnBzF,WAAW,EACXC,kBAAkB,EAClB7B,WAAW,EAKZ,GARoB;QASnB,MAAM,IAAI,CAAC9B,mBAAmB;QAC9B,MAAM,IAAI,CAACa,qBAAqB;QAChC,MAAM,IAAI,CAACK,qBAAqB;QAChC,MAAM,IAAI,CAACuC,kBAAkB,CAAC3B,aAAa4B,aAAaC;QACxD,MAAM,IAAI,CAACuC,0BAA0B;QACrC,MAAM,IAAI,CAACqC,uBAAuB;QAClC,MAAM,IAAI,CAAC5C,6BAA6B;QACxC,MAAM,IAAI,CAACgB,qBAAqB;QAChC,MAAM,IAAI,CAACqC,kBAAkB;QAE7B,IAAII,QAAQC,GAAG,CAACC,eAAe,IAAI,MAAM;YACvC,MAAM,IAAI,CAAChI,iBAAiB;QAC9B;IACF;IApnBAiI,YAAY,EACV5M,OAAO,EACP0G,OAAO,EACPjE,aAAa,EAKd,CAAE;aAtBKjB,kBAAiD,IAAI6D;aACrD5D,oBAAqD,IAAI4D;aACzD3D,oBAAkD,IAAI2D;aACtD1D,iBAA+C,IAAI0D;aACnDzD,gBAAiD,IAAIyD;aACrDxD,sBACN,IAAIwD;aACEvD,iBAA6C,IAAIuD;aACjDtD,eAA4C,IAAIsD;QAetD,IAAI,CAACrF,OAAO,GAAGA;QACf,IAAI,CAAC0G,OAAO,GAAGA;QACf,IAAI,CAACjE,aAAa,GAAGA;IACvB;AAymBF;AAEA,SAASW,gBAAgByJ,GAAwB;IAC/C,OAAO7J,OAAOkF,IAAI,CAAC2E,KAChBC,IAAI,GACJC,MAAM,CACL,CAACC,KAAKzL;QACJyL,GAAG,CAACzL,IAAI,GAAGsL,GAAG,CAACtL,IAAI;QACnB,OAAOyL;IACT,GACA,CAAC;AAEP"}