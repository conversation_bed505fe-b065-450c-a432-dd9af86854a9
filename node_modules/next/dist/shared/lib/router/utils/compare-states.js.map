{"version": 3, "sources": ["../../../../../src/shared/lib/router/utils/compare-states.ts"], "sourcesContent": ["import type { default as Router } from '../router'\n\nexport function compareRouterStates(a: Router['state'], b: Router['state']) {\n  const stateKeys = Object.keys(a)\n  if (stateKeys.length !== Object.keys(b).length) return false\n\n  for (let i = stateKeys.length; i--; ) {\n    const key = stateKeys[i]\n    if (key === 'query') {\n      const queryKeys = Object.keys(a.query)\n      if (queryKeys.length !== Object.keys(b.query).length) {\n        return false\n      }\n      for (let j = queryKeys.length; j--; ) {\n        const queryKey = queryKeys[j]\n        if (\n          !b.query.hasOwnProperty(queryKey) ||\n          a.query[queryKey] !== b.query[queryKey]\n        ) {\n          return false\n        }\n      }\n    } else if (\n      !b.hasOwnProperty(key) ||\n      a[key as keyof Router['state']] !== b[key as keyof Router['state']]\n    ) {\n      return false\n    }\n  }\n\n  return true\n}\n"], "names": ["compareRouterStates", "a", "b", "stateKeys", "Object", "keys", "length", "i", "key", "query<PERSON>eys", "query", "j", "query<PERSON><PERSON>", "hasOwnProperty"], "mappings": ";;;;+BAEgBA;;;eAAAA;;;AAAT,SAASA,oBAAoBC,CAAkB,EAAEC,CAAkB;IACxE,MAAMC,YAAYC,OAAOC,IAAI,CAACJ;IAC9B,IAAIE,UAAUG,MAAM,KAAKF,OAAOC,IAAI,CAACH,GAAGI,MAAM,EAAE,OAAO;IAEvD,IAAK,IAAIC,IAAIJ,UAAUG,MAAM,EAAEC,KAAO;QACpC,MAAMC,MAAML,SAAS,CAACI,EAAE;QACxB,IAAIC,QAAQ,SAAS;YACnB,MAAMC,YAAYL,OAAOC,IAAI,CAACJ,EAAES,KAAK;YACrC,IAAID,UAAUH,MAAM,KAAKF,OAAOC,IAAI,CAACH,EAAEQ,KAAK,EAAEJ,MAAM,EAAE;gBACpD,OAAO;YACT;YACA,IAAK,IAAIK,IAAIF,UAAUH,MAAM,EAAEK,KAAO;gBACpC,MAAMC,WAAWH,SAAS,CAACE,EAAE;gBAC7B,IACE,CAACT,EAAEQ,KAAK,CAACG,cAAc,CAACD,aACxBX,EAAES,KAAK,CAACE,SAAS,KAAKV,EAAEQ,KAAK,CAACE,SAAS,EACvC;oBACA,OAAO;gBACT;YACF;QACF,OAAO,IACL,CAACV,EAAEW,cAAc,CAACL,QAClBP,CAAC,CAACO,IAA6B,KAAKN,CAAC,CAACM,IAA6B,EACnE;YACA,OAAO;QACT;IACF;IAEA,OAAO;AACT"}