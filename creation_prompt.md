Build a comprehensive RAG-powered log analysis application similar to Kibana using Next.js frontend and Node.js backend. The application should enable natural language querying of structured logs and provide visual analytics for response times, status codes, and module-level failures.

**Log Format Specification:**
The application must parse logs with this exact structure:
```
[timestamp][level][module][sessionId][platform][customerId][clientIp][requestId][auditType][appName][responseTime][status][method][originUrl][extraData] : rawData
```

**Log Sources:**
Process logs from these specific directory paths:
- `/log/axisLogs/flights`
- `/log/axisLogs/hotels` 
- `/log/axisLogs/activities`
- `/log/axisLogs/pointTransfer`
- `/log/axisLogs/loyalty`
- `/log/axisLogs/common`

**Required Components:**

1. **Log Parser (Node.js)**
   - Create a robust parser to extract all structured fields from the log format into JSON objects
   - Handle malformed logs gracefully with error reporting
   - Support batch processing of log files
   - Include validation for each extracted field

2. **Vector Database & Embeddings**
   - Implement OpenAI embeddings or Sentence Transformers for log vectorization
   - Use Pinecone, Weaviate, or FAISS for vector storage
   - Index metadata fields: timestamp, status, module, requestId, customerId, sessionId
   - Support semantic search and similarity matching

3. **RAG Pipeline (Node.js Backend)**
   - Build retrieval system to find relevant logs based on queries
   - Implement LLM integration (OpenAI GPT-4, Claude, or Mistral) for:
     * Single log summarization
     * Multi-log root cause analysis
     * Natural language query interpretation
   - Create context-aware prompt engineering

4. **LLM Prompt Templates**
   - Template for single log analysis: "Analyze this log entry and identify potential issues..."
   - Template for root cause analysis: "Given these related log entries, identify the root cause..."
   - Template for query translation: "Convert this natural language query to structured filters..."
   - Template for trend analysis: "Analyze these logs over time and identify patterns..."

5. **Next.js Frontend with Modern UI**
   - Implement responsive design using Tailwind CSS
   - Create components:
     * Natural language search interface
     * Real-time query results display
     * Interactive charts using Recharts (response time distributions, status code breakdowns, error trends)
     * Filterable log table with pagination
     * Export functionality for filtered results
   - Add dark/light mode support

6. **Node.js API Backend**
   - RESTful API endpoints:
     * `POST /api/query` - Natural language log queries
     * `GET /api/logs` - Filtered log retrieval
     * `POST /api/upload` - Manual log file upload
     * `GET /api/analytics` - Dashboard metrics
   - Implement proper error handling and validation
   - Add rate limiting and authentication middleware

7. **Project Structure**
   ```
   /
   ├── apps/
   │   ├── frontend/          # Next.js application
   │   └── backend/           # Node.js API server
   ├── libs/
   │   ├── parsers/           # Log parsing utilities
   │   ├── embeddings/        # Vector generation logic
   │   ├── prompts/           # LLM prompt templates
   │   └── database/          # Vector DB operations
   ├── config/                # Environment configurations
   └── docker/                # Containerization files
   ```

**Technical Requirements:**
- Use TypeScript for type safety across both frontend and backend
- Implement proper error handling and logging
- Add comprehensive input validation
- Include unit and integration tests
- Support environment-based configuration
- Implement caching for frequently accessed data
- Add monitoring and health check endpoints

**Advanced Features:**
- Real-time log streaming and processing
- Session-based log grouping for better context
- Automated anomaly detection using statistical analysis
- Export capabilities (JSON, CSV, PDF reports)
- Role-based access control
- Log retention policies and archiving

**Performance Considerations:**
- Implement pagination for large result sets
- Use connection pooling for database operations
- Add Redis caching for frequent queries
- Optimize vector search with proper indexing
- Implement lazy loading for UI components

**Security Requirements:**
- Input sanitization for all user inputs
- API authentication using JWT tokens
- Rate limiting to prevent abuse
- Secure file upload validation
- Environment variable management for sensitive data

The solution should be production-ready, scalable, and easily extensible for additional log sources and analysis capabilities.