[2024-01-15 10:30:45.123][INFO][flights][sess123][web][cust456][192.168.1.100][req789][booking][FlightApp][1250][200][GET][https://example.com/api/flights/search][origin=NYC,destination=LAX] : User searched for flights from NYC to LAX
[2024-01-15 10:31:12.456][INFO][flights][sess123][web][cust456][192.168.1.100][req790][booking][FlightApp][890][200][POST][https://example.com/api/flights/book][flightId=FL123] : User booked flight FL123
[2024-01-15 10:32:05.789][ERROR][flights][sess124][mobile][cust457][192.168.1.101][req791][booking][FlightApp][5000][500][POST][https://example.com/api/flights/book][flightId=FL456] : Database connection timeout during booking
[2024-01-15 10:33:22.012][WARN][flights][sess125][web][cust458][192.168.1.102][req792][search][FlightApp][3200][429][GET][https://example.com/api/flights/search][origin=LAX,destination=NYC] : Rate limit exceeded for user
[2024-01-15 10:34:15.345][INFO][flights][sess126][api][cust459][192.168.1.103][req793][booking][FlightApp][1100][200][GET][https://example.com/api/flights/status][flightId=FL789] : Flight status check successful
[2024-01-15 10:35:30.678][ERROR][flights][sess127][web][cust460][192.168.1.104][req794][payment][FlightApp][2500][400][POST][https://example.com/api/flights/payment][bookingId=BK123] : Invalid payment method
[2024-01-15 10:36:45.901][INFO][flights][sess128][mobile][cust461][192.168.1.105][req795][search][FlightApp][950][200][GET][https://example.com/api/flights/search][origin=CHI,destination=MIA] : Flight search completed
[2024-01-15 10:37:12.234][WARN][flights][sess129][web][cust462][192.168.1.106][req796][booking][FlightApp][4500][503][POST][https://example.com/api/flights/book][flightId=FL999] : Service temporarily unavailable
[2024-01-15 10:38:05.567][INFO][flights][sess130][api][cust463][192.168.1.107][req797][cancellation][FlightApp][1800][200][DELETE][https://example.com/api/flights/cancel][bookingId=BK456] : Flight cancellation successful
[2024-01-15 10:39:22.890][ERROR][flights][sess131][mobile][cust464][192.168.1.108][req798][search][FlightApp][8000][504][GET][https://example.com/api/flights/search][origin=SEA,destination=BOS] : Gateway timeout error
