export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  timestamp: string;
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export interface LogEntry {
  id: string;
  timestamp: string;
  level: 'DEBUG' | 'INFO' | 'WARN' | 'ERROR' | 'FATAL';
  module: string;
  sessionId: string;
  platform: string;
  customerId: string;
  clientIp: string;
  requestId: string;
  auditType: string;
  appName: string;
  responseTime: number;
  status: number;
  method: string;
  originUrl: string;
  extraData?: string;
  rawData: string;
  sourceFile?: string;
  lineNumber?: number;
}

export interface SearchResult {
  id: string;
  score: number;
  metadata: LogEntry;
  text: string;
}

export interface QueryResult {
  answer: string;
  relevantLogs: SearchResult[];
  confidence: number;
  processingTime: number;
  sources: string[];
}

export interface AnalysisResult {
  analysis: string;
  insights: string[];
  recommendations: string[];
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  confidence: number;
}

export interface User {
  id: string;
  username: string;
  email: string;
  role: 'admin' | 'analyst' | 'viewer';
}

export interface AuthState {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
}

export interface LogFilter {
  level?: string;
  module?: string;
  status?: number[];
  timeRange?: {
    start: string;
    end: string;
  };
  platform?: string;
  customerId?: string;
  search?: string;
}

export interface ChartData {
  name: string;
  value: number;
  [key: string]: any;
}

export interface TimeSeriesData {
  timestamp: string;
  value: number;
  [key: string]: any;
}

export interface DashboardMetrics {
  totalLogs: number;
  errorRate: number;
  avgResponseTime: number;
  activeUsers: number;
  topErrors: Array<{
    message: string;
    count: number;
  }>;
  responseTimeDistribution: ChartData[];
  statusCodeDistribution: ChartData[];
  moduleActivity: ChartData[];
  timeSeriesData: TimeSeriesData[];
}

export interface UploadJob {
  id: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  sourceFile: string;
  totalLines: number;
  processedLines: number;
  errors: number;
  startedAt: string;
  completedAt?: string;
  error?: string;
}

export interface Theme {
  mode: 'light' | 'dark';
}

export interface AppSettings {
  theme: Theme;
  autoRefresh: boolean;
  refreshInterval: number;
  defaultPageSize: number;
  enableNotifications: boolean;
}
