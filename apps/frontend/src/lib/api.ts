import axios, { AxiosInstance, AxiosResponse } from 'axios';
import { ApiResponse, LogEntry, QueryResult, AnalysisResult, User, UploadJob } from '@/types';

class ApiClient {
  private client: AxiosInstance;

  constructor() {
    this.client = axios.create({
      baseURL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001',
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Request interceptor to add auth token
    this.client.interceptors.request.use(
      (config) => {
        const token = localStorage.getItem('auth_token');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => Promise.reject(error)
    );

    // Response interceptor for error handling
    this.client.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.response?.status === 401) {
          localStorage.removeItem('auth_token');
          window.location.href = '/login';
        }
        return Promise.reject(error);
      }
    );
  }

  // Auth endpoints
  async login(username: string, password: string): Promise<{ token: string; user: User }> {
    const response: AxiosResponse<ApiResponse<{ token: string; user: User }>> = 
      await this.client.post('/api/auth/login', { username, password });
    return response.data.data!;
  }

  async logout(): Promise<void> {
    await this.client.post('/api/auth/logout');
    localStorage.removeItem('auth_token');
  }

  async getCurrentUser(): Promise<User> {
    const response: AxiosResponse<ApiResponse<User>> = 
      await this.client.get('/api/auth/me');
    return response.data.data!;
  }

  // Query endpoints
  async queryLogs(query: string, options?: {
    limit?: number;
    threshold?: number;
    filters?: any;
  }): Promise<QueryResult> {
    const response: AxiosResponse<ApiResponse<QueryResult>> = 
      await this.client.post('/api/query', {
        query,
        ...options,
      });
    return response.data.data!;
  }

  async findSimilarLogs(logEntry: LogEntry, options?: {
    limit?: number;
    threshold?: number;
  }): Promise<any[]> {
    const response: AxiosResponse<ApiResponse<any[]>> = 
      await this.client.post('/api/query/similar', {
        logEntry,
        ...options,
      });
    return response.data.data!;
  }

  async translateQuery(query: string): Promise<any> {
    const response: AxiosResponse<ApiResponse<any>> = 
      await this.client.post('/api/query/translate', { query });
    return response.data.data!;
  }

  async getQueryStats(): Promise<any> {
    const response: AxiosResponse<ApiResponse<any>> = 
      await this.client.get('/api/query/stats');
    return response.data.data!;
  }

  // Analysis endpoints
  async analyzeSingleLog(logEntry: LogEntry): Promise<AnalysisResult> {
    const response: AxiosResponse<ApiResponse<AnalysisResult>> = 
      await this.client.post('/api/analysis/single', { logEntry });
    return response.data.data!;
  }

  async performRootCauseAnalysis(
    logEntries: LogEntry[], 
    timeRange: string, 
    context?: string
  ): Promise<AnalysisResult> {
    const response: AxiosResponse<ApiResponse<AnalysisResult>> = 
      await this.client.post('/api/analysis/root-cause', {
        logEntries,
        timeRange,
        context,
      });
    return response.data.data!;
  }

  async analyzeTrends(options: {
    timeRange: { start: string; end: string };
    modules?: string[];
    groupBy?: 'hour' | 'day' | 'week';
    metrics?: string[];
  }): Promise<any> {
    const response: AxiosResponse<ApiResponse<any>> = 
      await this.client.post('/api/analysis/trend', options);
    return response.data.data!;
  }

  async detectAnomalies(logEntry: LogEntry, historicalData?: any): Promise<any> {
    const response: AxiosResponse<ApiResponse<any>> = 
      await this.client.post('/api/analysis/anomaly', {
        logEntry,
        historicalData,
      });
    return response.data.data!;
  }

  async analyzePerformance(options: {
    timeRange: { start: string; end: string };
    module?: string;
    metrics?: string[];
  }): Promise<any> {
    const response: AxiosResponse<ApiResponse<any>> = 
      await this.client.post('/api/analysis/performance', options);
    return response.data.data!;
  }

  // Upload endpoints
  async uploadFiles(files: File[], options?: {
    skipMalformed?: boolean;
    batchSize?: number;
  }): Promise<{ jobs: UploadJob[] }> {
    const formData = new FormData();
    files.forEach(file => formData.append('files', file));

    if (options) {
      formData.append('options', JSON.stringify(options));
    }

    const response: AxiosResponse<ApiResponse<{ jobs: UploadJob[] }>> =
      await this.client.post('/api/upload', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
    return response.data.data!;
  }

  async processLocalPath(filePath: string, options?: {
    skipMalformed?: boolean;
    batchSize?: number;
  }): Promise<{ jobs: UploadJob[] }> {
    const response: AxiosResponse<ApiResponse<{ jobs: UploadJob[] }>> =
      await this.client.post('/api/upload/local-path', {
        filePath,
        options,
      });
    return response.data.data!;
  }

  async getUploadJob(jobId: string): Promise<UploadJob> {
    const response: AxiosResponse<ApiResponse<UploadJob>> = 
      await this.client.get(`/api/upload/jobs/${jobId}`);
    return response.data.data!;
  }

  async getAllUploadJobs(): Promise<UploadJob[]> {
    const response: AxiosResponse<ApiResponse<UploadJob[]>> = 
      await this.client.get('/api/upload/jobs');
    return response.data.data!;
  }

  async cancelUploadJob(jobId: string): Promise<UploadJob> {
    const response: AxiosResponse<ApiResponse<UploadJob>> = 
      await this.client.delete(`/api/upload/jobs/${jobId}`);
    return response.data.data!;
  }

  // Logs endpoints
  async getLogs(options?: {
    page?: number;
    limit?: number;
    filters?: any;
  }): Promise<{ logs: LogEntry[]; pagination: any }> {
    const response: AxiosResponse<ApiResponse<{ logs: LogEntry[]; pagination: any }>> = 
      await this.client.get('/api/logs', { params: options });
    return response.data.data!;
  }

  // Analytics endpoints
  async getAnalytics(options?: {
    timeRange?: { start: string; end: string };
    modules?: string[];
    groupBy?: 'hour' | 'day' | 'week';
    metrics?: string[];
  }): Promise<any> {
    const response: AxiosResponse<ApiResponse<any>> = 
      await this.client.get('/api/analytics', { params: options });
    return response.data.data!;
  }

  // Health check
  async getHealth(): Promise<any> {
    const response: AxiosResponse<ApiResponse<any>> = 
      await this.client.get('/health');
    return response.data.data!;
  }
}

export const apiClient = new ApiClient();
export default apiClient;
