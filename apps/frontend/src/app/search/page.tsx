'use client';

import React, { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import Layout from '@/components/layout/Layout';
import SearchInterface from '@/components/search/SearchInterface';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';
import { SearchResult } from '@/types';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { formatDate, getLogLevelColor, getStatusColor } from '@/lib/utils';

export default function SearchPage() {
  const { isAuthenticated, isLoading } = useAuth();
  const router = useRouter();
  const [selectedResult, setSelectedResult] = useState<SearchResult | null>(null);

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/login');
    }
  }, [isAuthenticated, isLoading, router]);

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return null;
  }

  return (
    <Layout>
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Search Logs</h1>
          <p className="text-gray-600 dark:text-gray-400">
            Use natural language to search and analyze your logs
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-2">
            <SearchInterface onResultSelect={setSelectedResult} />
          </div>

          {/* Log Detail Panel */}
          <div className="lg:col-span-1">
            {selectedResult ? (
              <Card className="sticky top-6">
                <CardHeader>
                  <CardTitle>Log Details</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center gap-2">
                      <span className={`px-2 py-1 rounded text-xs font-medium ${getLogLevelColor(selectedResult.metadata.level)}`}>
                        {selectedResult.metadata.level}
                      </span>
                      <span className="text-sm text-gray-600 dark:text-gray-400">
                        {selectedResult.metadata.module}
                      </span>
                      <span className={`text-sm font-medium ${getStatusColor(selectedResult.metadata.status)}`}>
                        {selectedResult.metadata.status}
                      </span>
                    </div>

                    <div className="space-y-2 text-sm">
                      <div>
                        <span className="font-medium text-gray-700 dark:text-gray-300">Timestamp:</span>
                        <p className="text-gray-600 dark:text-gray-400">{formatDate(selectedResult.metadata.timestamp)}</p>
                      </div>

                      <div>
                        <span className="font-medium text-gray-700 dark:text-gray-300">Response Time:</span>
                        <p className="text-gray-600 dark:text-gray-400">{selectedResult.metadata.responseTime}ms</p>
                      </div>

                      <div>
                        <span className="font-medium text-gray-700 dark:text-gray-300">Method:</span>
                        <p className="text-gray-600 dark:text-gray-400">{selectedResult.metadata.method}</p>
                      </div>

                      <div>
                        <span className="font-medium text-gray-700 dark:text-gray-300">URL:</span>
                        <p className="text-gray-600 dark:text-gray-400 break-all">{selectedResult.metadata.originUrl}</p>
                      </div>

                      <div>
                        <span className="font-medium text-gray-700 dark:text-gray-300">Session ID:</span>
                        <p className="text-gray-600 dark:text-gray-400">{selectedResult.metadata.sessionId}</p>
                      </div>

                      <div>
                        <span className="font-medium text-gray-700 dark:text-gray-300">Customer ID:</span>
                        <p className="text-gray-600 dark:text-gray-400">{selectedResult.metadata.customerId}</p>
                      </div>

                      <div>
                        <span className="font-medium text-gray-700 dark:text-gray-300">Client IP:</span>
                        <p className="text-gray-600 dark:text-gray-400">{selectedResult.metadata.clientIp}</p>
                      </div>

                      <div>
                        <span className="font-medium text-gray-700 dark:text-gray-300">Platform:</span>
                        <p className="text-gray-600 dark:text-gray-400">{selectedResult.metadata.platform}</p>
                      </div>

                      <div>
                        <span className="font-medium text-gray-700 dark:text-gray-300">App Name:</span>
                        <p className="text-gray-600 dark:text-gray-400">{selectedResult.metadata.appName}</p>
                      </div>

                      <div>
                        <span className="font-medium text-gray-700 dark:text-gray-300">Audit Type:</span>
                        <p className="text-gray-600 dark:text-gray-400">{selectedResult.metadata.auditType}</p>
                      </div>

                      <div>
                        <span className="font-medium text-gray-700 dark:text-gray-300">Request ID:</span>
                        <p className="text-gray-600 dark:text-gray-400">{selectedResult.metadata.requestId}</p>
                      </div>

                      <div>
                        <span className="font-medium text-gray-700 dark:text-gray-300">Similarity Score:</span>
                        <p className="text-gray-600 dark:text-gray-400">{selectedResult.score.toFixed(3)}</p>
                      </div>

                      <div>
                        <span className="font-medium text-gray-700 dark:text-gray-300">Log Text:</span>
                        <p className="text-gray-600 dark:text-gray-400 whitespace-pre-wrap">{selectedResult.text}</p>
                      </div>

                      {selectedResult.metadata.sourceFile && (
                        <div>
                          <span className="font-medium text-gray-700 dark:text-gray-300">Source:</span>
                          <p className="text-gray-600 dark:text-gray-400">
                            {selectedResult.metadata.sourceFile}
                            {selectedResult.metadata.lineNumber && `:${selectedResult.metadata.lineNumber}`}
                          </p>
                        </div>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ) : (
              <Card className="sticky top-6">
                <CardContent className="pt-6">
                  <div className="text-center text-gray-500 dark:text-gray-400">
                    <p>Select a log entry to view details</p>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </div>
    </Layout>
  );
}
