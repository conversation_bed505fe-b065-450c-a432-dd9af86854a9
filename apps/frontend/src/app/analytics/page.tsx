'use client';

import React, { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import Layout from '@/components/layout/Layout';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import { ChartBarIcon, CalendarIcon, FunnelIcon } from '@heroicons/react/24/outline';
import { useQuery } from '@tanstack/react-query';
import apiClient from '@/lib/api';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
} from 'recharts';

const COLORS = ['#3B82F6', '#EF4444', '#F59E0B', '#10B981', '#8B5CF6', '#F97316'];

export default function AnalyticsPage() {
  const { isAuthenticated, isLoading, user } = useAuth();
  const router = useRouter();
  const [timeRange, setTimeRange] = useState({
    start: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 7 days ago
    end: new Date().toISOString().split('T')[0], // today
  });
  const [selectedModules, setSelectedModules] = useState<string[]>([]);
  const [groupBy, setGroupBy] = useState<'hour' | 'day' | 'week'>('day');

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/login');
    }
  }, [isAuthenticated, isLoading, router]);

  // Check if user has analytics permissions
  const canViewAnalytics = user?.role === 'admin' || user?.role === 'analyst';

  // Fetch analytics data
  const { data: analytics, isLoading: analyticsLoading, refetch } = useQuery({
    queryKey: ['analytics', timeRange, selectedModules, groupBy],
    queryFn: () => apiClient.getAnalytics({
      timeRange: {
        start: new Date(timeRange.start).toISOString(),
        end: new Date(timeRange.end).toISOString(),
      },
      modules: selectedModules.length > 0 ? selectedModules : undefined,
      groupBy,
    }),
    enabled: isAuthenticated && canViewAnalytics,
  });

  const handleApplyFilters = () => {
    refetch();
  };

  const modules = ['flights', 'hotels', 'activities', 'pointTransfer', 'loyalty', 'common'];

  // Mock data for demonstration
  const mockAnalytics = {
    summary: {
      totalRequests: 45230,
      errorRate: 2.3,
      avgResponseTime: 1250,
      peakHour: '14:00',
    },
    trends: [
      { date: '2024-01-08', requests: 3200, errors: 45, avgResponseTime: 1100 },
      { date: '2024-01-09', requests: 3800, errors: 52, avgResponseTime: 1200 },
      { date: '2024-01-10', requests: 4100, errors: 38, avgResponseTime: 1150 },
      { date: '2024-01-11', requests: 3900, errors: 67, avgResponseTime: 1300 },
      { date: '2024-01-12', requests: 4500, errors: 89, avgResponseTime: 1400 },
      { date: '2024-01-13', requests: 4200, errors: 76, avgResponseTime: 1250 },
      { date: '2024-01-14', requests: 3700, errors: 43, avgResponseTime: 1100 },
    ],
    moduleBreakdown: [
      { name: 'flights', requests: 18500, errors: 234 },
      { name: 'hotels', requests: 12300, errors: 156 },
      { name: 'activities', requests: 8900, errors: 89 },
      { name: 'loyalty', requests: 3200, errors: 45 },
      { name: 'pointTransfer', requests: 1800, errors: 23 },
      { name: 'common', requests: 530, errors: 12 },
    ],
    statusCodes: [
      { name: '2xx', value: 85.2, count: 38567 },
      { name: '3xx', value: 8.1, count: 3664 },
      { name: '4xx', value: 4.4, count: 1991 },
      { name: '5xx', value: 2.3, count: 1041 },
    ],
  };

  const displayData = analytics?.data || mockAnalytics;

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return null;
  }

  if (!canViewAnalytics) {
    return (
      <Layout>
        <div className="text-center py-12">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">Access Denied</h1>
          <p className="text-gray-600 dark:text-gray-400">
            You don't have permission to view analytics. Contact an administrator for access.
          </p>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Analytics</h1>
          <p className="text-gray-600 dark:text-gray-400">
            Detailed analysis and insights from your log data
          </p>
        </div>

        {/* Filters */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FunnelIcon className="h-5 w-5" />
              Filters
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div>
                <Input
                  label="Start Date"
                  type="date"
                  value={timeRange.start}
                  onChange={(e) => setTimeRange(prev => ({ ...prev, start: e.target.value }))}
                />
              </div>
              <div>
                <Input
                  label="End Date"
                  type="date"
                  value={timeRange.end}
                  onChange={(e) => setTimeRange(prev => ({ ...prev, end: e.target.value }))}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Group By
                </label>
                <select
                  value={groupBy}
                  onChange={(e) => setGroupBy(e.target.value as 'hour' | 'day' | 'week')}
                  className="w-full rounded-md border border-gray-300 px-3 py-2 dark:border-gray-600 dark:bg-gray-800"
                >
                  <option value="hour">Hour</option>
                  <option value="day">Day</option>
                  <option value="week">Week</option>
                </select>
              </div>
              <div className="flex items-end">
                <Button onClick={handleApplyFilters} loading={analyticsLoading}>
                  Apply Filters
                </Button>
              </div>
            </div>

            <div className="mt-4">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Modules (leave empty for all)
              </label>
              <div className="flex flex-wrap gap-2">
                {modules.map((module) => (
                  <label key={module} className="flex items-center">
                    <input
                      type="checkbox"
                      checked={selectedModules.includes(module)}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setSelectedModules(prev => [...prev, module]);
                        } else {
                          setSelectedModules(prev => prev.filter(m => m !== module));
                        }
                      }}
                      className="mr-2"
                    />
                    <span className="text-sm">{module}</span>
                  </label>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card>
            <CardContent className="p-6">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Requests</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                  {(displayData.summary?.totalRequests || 0).toLocaleString()}
                </p>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Error Rate</p>
                <p className="text-2xl font-bold text-red-600 dark:text-red-400">
                  {displayData.summary?.errorRate || 0}%
                </p>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Avg Response Time</p>
                <p className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                  {displayData.summary?.avgResponseTime || 0}ms
                </p>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Peak Hour</p>
                <p className="text-2xl font-bold text-green-600 dark:text-green-400">
                  {displayData.summary?.peakHour || 'N/A'}
                </p>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Charts */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Trends Chart */}
          <Card>
            <CardHeader>
              <CardTitle>Request Trends</CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <LineChart data={displayData.trends || []}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="date" />
                  <YAxis />
                  <Tooltip />
                  <Line type="monotone" dataKey="requests" stroke="#3B82F6" strokeWidth={2} />
                  <Line type="monotone" dataKey="errors" stroke="#EF4444" strokeWidth={2} />
                </LineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          {/* Status Codes */}
          <Card>
            <CardHeader>
              <CardTitle>Status Code Distribution</CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <PieChart>
                  <Pie
                    data={displayData.statusCodes || []}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    label={({ name, value }) => `${name}: ${value}%`}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                  >
                    {(displayData.statusCodes || []).map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip />
                </PieChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </div>

        {/* Module Breakdown */}
        <Card>
          <CardHeader>
            <CardTitle>Module Activity</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={400}>
              <BarChart data={displayData.moduleBreakdown || []}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="requests" fill="#3B82F6" name="Requests" />
                <Bar dataKey="errors" fill="#EF4444" name="Errors" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>
    </Layout>
  );
}
