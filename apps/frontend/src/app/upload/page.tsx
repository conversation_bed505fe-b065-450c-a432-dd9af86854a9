'use client';

import React, { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import Layout from '@/components/layout/Layout';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import { CloudArrowUpIcon, DocumentIcon, CheckCircleIcon, XCircleIcon } from '@heroicons/react/24/outline';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import apiClient from '@/lib/api';
import { UploadJob } from '@/types';

export default function UploadPage() {
  const { isAuthenticated, isLoading, user } = useAuth();
  const router = useRouter();
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const [isDragging, setIsDragging] = useState(false);
  const [localPath, setLocalPath] = useState('');
  const [uploadMode, setUploadMode] = useState<'file' | 'path'>('file');
  const queryClient = useQueryClient();

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/login');
    }
  }, [isAuthenticated, isLoading, router]);

  // Check if user has upload permissions
  const canUpload = user?.role === 'admin' || user?.role === 'analyst';

  // Fetch upload jobs
  const { data: jobs, isLoading: jobsLoading } = useQuery({
    queryKey: ['upload-jobs'],
    queryFn: () => apiClient.getAllUploadJobs(),
    enabled: isAuthenticated && canUpload,
    refetchInterval: 2000, // Refresh every 2 seconds to show progress
  });

  // Upload mutation
  const uploadMutation = useMutation({
    mutationFn: (files: File[]) => apiClient.uploadFiles(files),
    onSuccess: () => {
      setSelectedFiles([]);
      queryClient.invalidateQueries({ queryKey: ['upload-jobs'] });
    },
  });

  // Local path processing mutation
  const pathMutation = useMutation({
    mutationFn: (filePath: string) => apiClient.processLocalPath(filePath),
    onSuccess: () => {
      setLocalPath('');
      queryClient.invalidateQueries({ queryKey: ['upload-jobs'] });
    },
  });

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    setSelectedFiles(files);
  };

  const handleDrop = (event: React.DragEvent) => {
    event.preventDefault();
    setIsDragging(false);
    const files = Array.from(event.dataTransfer.files);
    setSelectedFiles(files);
  };

  const handleDragOver = (event: React.DragEvent) => {
    event.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = () => {
    setIsDragging(false);
  };

  const handleUpload = () => {
    if (selectedFiles.length > 0) {
      uploadMutation.mutate(selectedFiles);
    }
  };

  const handleProcessPath = () => {
    if (localPath.trim()) {
      pathMutation.mutate(localPath.trim());
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircleIcon className="h-5 w-5 text-green-500" />;
      case 'failed':
        return <XCircleIcon className="h-5 w-5 text-red-500" />;
      case 'processing':
        return <div className="h-5 w-5 border-2 border-blue-500 border-t-transparent rounded-full animate-spin" />;
      default:
        return <div className="h-5 w-5 bg-gray-300 rounded-full" />;
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return null;
  }

  if (!canUpload) {
    return (
      <Layout>
        <div className="text-center py-12">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">Access Denied</h1>
          <p className="text-gray-600 dark:text-gray-400">
            You don't have permission to upload files. Contact an administrator for access.
          </p>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Upload Log Files</h1>
          <p className="text-gray-600 dark:text-gray-400">
            Upload log files for analysis and indexing
          </p>
        </div>

        {/* Upload Section */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CloudArrowUpIcon className="h-5 w-5" />
              Process Log Files
            </CardTitle>
          </CardHeader>
          <CardContent>
            {/* Mode Selector */}
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Processing Mode
              </label>
              <div className="flex gap-4">
                <label className="flex items-center">
                  <input
                    type="radio"
                    value="file"
                    checked={uploadMode === 'file'}
                    onChange={(e) => setUploadMode(e.target.value as 'file' | 'path')}
                    className="mr-2"
                  />
                  <span>Upload Files</span>
                </label>
                <label className="flex items-center">
                  <input
                    type="radio"
                    value="path"
                    checked={uploadMode === 'path'}
                    onChange={(e) => setUploadMode(e.target.value as 'file' | 'path')}
                    className="mr-2"
                  />
                  <span>Local File Path</span>
                </label>
              </div>
            </div>
            {uploadMode === 'file' ? (
              <div
                className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
                  isDragging
                    ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                    : 'border-gray-300 dark:border-gray-600'
                }`}
                onDrop={handleDrop}
                onDragOver={handleDragOver}
                onDragLeave={handleDragLeave}
              >
                <CloudArrowUpIcon className="mx-auto h-12 w-12 text-gray-400" />
                <div className="mt-4">
                  <label htmlFor="file-upload" className="cursor-pointer">
                    <span className="mt-2 block text-sm font-medium text-gray-900 dark:text-gray-100">
                      Drop files here or click to browse
                    </span>
                    <input
                      id="file-upload"
                      name="file-upload"
                      type="file"
                      multiple
                      accept=".log,.txt,.log.*"
                      className="sr-only"
                      onChange={handleFileSelect}
                    />
                  </label>
                  <p className="mt-1 text-xs text-gray-500">
                    Supports .log, .txt, and dated log files (e.g., .log.2025-07-14) up to 10GB each
                  </p>
                </div>
              </div>
            ) : (
              <div className="space-y-4">
                <div>
                  <Input
                    label="Local File Path"
                    value={localPath}
                    onChange={(e) => setLocalPath(e.target.value)}
                    placeholder="/path/to/your/logfile.log or /path/to/logs/directory"
                    helperText="Enter the full path to a log file or directory. Supports files like business_info.log.2025-07-14"
                  />
                </div>
                <div className="bg-blue-50 border border-blue-200 rounded-md p-4 dark:bg-blue-900/20 dark:border-blue-700">
                  <h4 className="text-sm font-medium text-blue-800 dark:text-blue-200 mb-2">
                    Example Paths:
                  </h4>
                  <ul className="text-xs text-blue-700 dark:text-blue-300 space-y-1">
                    <li>• <code>/var/log/application.log</code> - Single log file</li>
                    <li>• <code>/var/log/business_info.log.2025-07-14</code> - Dated log file</li>
                    <li>• <code>/var/log/myapp/</code> - Directory containing log files</li>
                    <li>• <code>./logs/sample.log</code> - Relative path from server root</li>
                    <li>• <code>/home/<USER>/logs/</code> - User directory with logs</li>
                  </ul>
                </div>
                <Button
                  onClick={handleProcessPath}
                  loading={pathMutation.isPending}
                  disabled={!localPath.trim()}
                  className="w-full"
                >
                  Process Local File(s)
                </Button>
              </div>
            )}

            {uploadMode === 'file' && selectedFiles.length > 0 && (
              <div className="mt-4">
                <h3 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
                  Selected Files ({selectedFiles.length})
                </h3>
                <div className="space-y-2">
                  {selectedFiles.map((file, index) => (
                    <div key={index} className="flex items-center gap-2 text-sm">
                      <DocumentIcon className="h-4 w-4 text-gray-400" />
                      <span className="flex-1">{file.name}</span>
                      <span className="text-gray-500">
                        {(file.size / 1024 / 1024).toFixed(2)} MB
                      </span>
                    </div>
                  ))}
                </div>
                <div className="mt-4">
                  <Button
                    onClick={handleUpload}
                    loading={uploadMutation.isPending}
                    disabled={selectedFiles.length === 0}
                  >
                    Upload Files
                  </Button>
                </div>
              </div>
            )}

            {(uploadMutation.error || pathMutation.error) && (
              <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-md dark:bg-red-900/20 dark:border-red-700">
                <p className="text-red-700 dark:text-red-300">
                  {uploadMode === 'file' ? 'Upload' : 'Processing'} failed: {
                    (uploadMutation.error || pathMutation.error) instanceof Error
                      ? (uploadMutation.error || pathMutation.error)?.message
                      : 'Unknown error'
                  }
                </p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Upload Jobs */}
        <Card>
          <CardHeader>
            <CardTitle>Upload History</CardTitle>
          </CardHeader>
          <CardContent>
            {jobsLoading ? (
              <div className="text-center py-4">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
              </div>
            ) : jobs && jobs.length > 0 ? (
              <div className="space-y-3">
                {jobs.map((job: UploadJob) => (
                  <div key={job.id} className="flex items-center justify-between p-3 border border-gray-200 dark:border-gray-700 rounded-lg">
                    <div className="flex items-center gap-3">
                      {getStatusIcon(job.status)}
                      <div>
                        <p className="font-medium text-gray-900 dark:text-gray-100">{job.sourceFile}</p>
                        <p className="text-sm text-gray-500">
                          {job.status === 'processing' 
                            ? `Processing: ${job.processedLines}/${job.totalLines} lines`
                            : job.status === 'completed'
                            ? `Completed: ${job.processedLines} lines processed, ${job.errors} errors`
                            : job.status === 'failed'
                            ? `Failed: ${job.error}`
                            : 'Pending'
                          }
                        </p>
                      </div>
                    </div>
                    <div className="text-right text-sm text-gray-500">
                      <p>{new Date(job.startedAt).toLocaleString()}</p>
                      {job.completedAt && (
                        <p>Completed: {new Date(job.completedAt).toLocaleString()}</p>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                <DocumentIcon className="mx-auto h-12 w-12 text-gray-300" />
                <p className="mt-2">No upload history yet</p>
                <p className="text-sm">Upload some log files to get started</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </Layout>
  );
}
