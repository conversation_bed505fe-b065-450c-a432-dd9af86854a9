'use client';

import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { MagnifyingGlassIcon, SparklesIcon } from '@heroicons/react/24/outline';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { QueryResult, SearchResult } from '@/types';
import apiClient from '@/lib/api';
import { formatDate, getLogLevelColor, getStatusColor } from '@/lib/utils';

interface SearchInterfaceProps {
  onResultSelect?: (result: SearchResult) => void;
}

export default function SearchInterface({ onResultSelect }: SearchInterfaceProps) {
  const [query, setQuery] = useState('');
  const [searchQuery, setSearchQuery] = useState('');
  const [searchOptions, setSearchOptions] = useState({
    limit: 10,
    threshold: 0.7,
  });

  const { data: queryResult, isLoading, error, refetch } = useQuery({
    queryKey: ['search', searchQuery, searchOptions],
    queryFn: () => apiClient.queryLogs(searchQuery, searchOptions),
    enabled: !!searchQuery,
  });

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (query.trim()) {
      setSearchQuery(query.trim());
    }
  };

  const handleExampleQuery = (exampleQuery: string) => {
    setQuery(exampleQuery);
    setSearchQuery(exampleQuery);
  };

  const exampleQueries = [
    'Show me all error logs from the flights module',
    'Find slow requests with response time over 5 seconds',
    'Database connection failures in the last hour',
    'Authentication errors for customer ID 12345',
    'All 500 status codes from the hotels module',
  ];

  return (
    <div className="space-y-6">
      {/* Search Form */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MagnifyingGlassIcon className="h-5 w-5" />
            Natural Language Log Search
          </CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSearch} className="space-y-4">
            <div className="flex gap-2">
              <Input
                value={query}
                onChange={(e) => setQuery(e.target.value)}
                placeholder="Ask anything about your logs... e.g., 'Show me all errors in the last hour'"
                className="flex-1"
              />
              <Button type="submit" loading={isLoading} disabled={!query.trim()}>
                <MagnifyingGlassIcon className="h-4 w-4 mr-2" />
                Search
              </Button>
            </div>

            {/* Search Options */}
            <div className="flex gap-4 text-sm">
              <div>
                <label className="block text-gray-700 dark:text-gray-300 mb-1">
                  Max Results
                </label>
                <select
                  value={searchOptions.limit}
                  onChange={(e) => setSearchOptions(prev => ({ ...prev, limit: parseInt(e.target.value) }))}
                  className="rounded border border-gray-300 px-2 py-1 dark:border-gray-600 dark:bg-gray-800"
                >
                  <option value={5}>5</option>
                  <option value={10}>10</option>
                  <option value={20}>20</option>
                  <option value={50}>50</option>
                </select>
              </div>
              <div>
                <label className="block text-gray-700 dark:text-gray-300 mb-1">
                  Similarity Threshold
                </label>
                <select
                  value={searchOptions.threshold}
                  onChange={(e) => setSearchOptions(prev => ({ ...prev, threshold: parseFloat(e.target.value) }))}
                  className="rounded border border-gray-300 px-2 py-1 dark:border-gray-600 dark:bg-gray-800"
                >
                  <option value={0.5}>0.5 (Loose)</option>
                  <option value={0.7}>0.7 (Balanced)</option>
                  <option value={0.8}>0.8 (Strict)</option>
                  <option value={0.9}>0.9 (Very Strict)</option>
                </select>
              </div>
            </div>
          </form>

          {/* Example Queries */}
          <div className="mt-6">
            <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 flex items-center gap-1">
              <SparklesIcon className="h-4 w-4" />
              Try these examples:
            </h3>
            <div className="flex flex-wrap gap-2">
              {exampleQueries.map((example, index) => (
                <button
                  key={index}
                  onClick={() => handleExampleQuery(example)}
                  className="text-xs bg-blue-50 text-blue-700 px-2 py-1 rounded hover:bg-blue-100 dark:bg-blue-900 dark:text-blue-300 dark:hover:bg-blue-800"
                >
                  {example}
                </button>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Search Results */}
      {error && (
        <Card>
          <CardContent className="pt-6">
            <div className="text-red-600 dark:text-red-400">
              Error: {error instanceof Error ? error.message : 'Search failed'}
            </div>
          </CardContent>
        </Card>
      )}

      {queryResult && (
        <div className="space-y-4">
          {/* AI Answer */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>AI Analysis</span>
                <div className="flex items-center gap-2 text-sm text-gray-500">
                  <span>Confidence: {(queryResult.confidence * 100).toFixed(0)}%</span>
                  <span>•</span>
                  <span>{queryResult.processingTime}ms</span>
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="prose dark:prose-invert max-w-none">
                <p className="whitespace-pre-wrap">{queryResult.answer}</p>
              </div>
              {queryResult.sources.length > 0 && (
                <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Sources: {queryResult.sources.join(', ')}
                  </p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Relevant Logs */}
          <Card>
            <CardHeader>
              <CardTitle>
                Relevant Log Entries ({queryResult.relevantLogs.length})
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {queryResult.relevantLogs.map((result, index) => (
                  <div
                    key={index}
                    className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-gray-800 cursor-pointer transition-colors"
                    onClick={() => onResultSelect?.(result)}
                  >
                    <div className="flex items-start justify-between mb-2">
                      <div className="flex items-center gap-2">
                        <span className={`px-2 py-1 rounded text-xs font-medium ${getLogLevelColor(result.metadata.level)}`}>
                          {result.metadata.level}
                        </span>
                        <span className="text-sm text-gray-600 dark:text-gray-400">
                          {result.metadata.module}
                        </span>
                        <span className={`text-sm font-medium ${getStatusColor(result.metadata.status)}`}>
                          {result.metadata.status}
                        </span>
                      </div>
                      <div className="text-right text-sm text-gray-500">
                        <div>Score: {result.score.toFixed(3)}</div>
                        <div>{formatDate(result.metadata.timestamp)}</div>
                      </div>
                    </div>
                    <div className="text-sm text-gray-900 dark:text-gray-100 mb-2">
                      {result.text}
                    </div>
                    <div className="flex items-center gap-4 text-xs text-gray-500">
                      <span>Response: {result.metadata.responseTime}ms</span>
                      <span>Session: {result.metadata.sessionId}</span>
                      <span>Customer: {result.metadata.customerId}</span>
                      <span>IP: {result.metadata.clientIp}</span>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}
