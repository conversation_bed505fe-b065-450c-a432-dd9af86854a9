'use client';

import React from 'react';
import { useQuery } from '@tanstack/react-query';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  <PERSON><PERSON>hart,
  Pie,
  Cell,
  LineChart,
  Line,
} from 'recharts';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { DashboardMetrics } from '@/types';
import apiClient from '@/lib/api';
import { formatDuration } from '@/lib/utils';

const COLORS = ['#3B82F6', '#EF4444', '#F59E0B', '#10B981', '#8B5CF6', '#F97316'];

export default function Dashboard() {
  const { data: metrics, isLoading, error } = useQuery({
    queryKey: ['dashboard-metrics'],
    queryFn: () => apiClient.getAnalytics({
      timeRange: {
        start: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(), // Last 24 hours
        end: new Date().toISOString(),
      },
      groupBy: 'hour',
    }),
    refetchInterval: 30000, // Refresh every 30 seconds
  });

  // Mock data for demonstration
  const mockMetrics: DashboardMetrics = {
    totalLogs: 125430,
    errorRate: 2.3,
    avgResponseTime: 1250,
    activeUsers: 1847,
    topErrors: [
      { message: 'Database connection timeout', count: 45 },
      { message: 'Authentication failed', count: 32 },
      { message: 'Service unavailable', count: 28 },
      { message: 'Invalid request format', count: 19 },
      { message: 'Rate limit exceeded', count: 15 },
    ],
    responseTimeDistribution: [
      { name: '0-100ms', value: 35 },
      { name: '100-500ms', value: 42 },
      { name: '500ms-1s', value: 15 },
      { name: '1-5s', value: 6 },
      { name: '5s+', value: 2 },
    ],
    statusCodeDistribution: [
      { name: '2xx', value: 85.2 },
      { name: '3xx', value: 8.1 },
      { name: '4xx', value: 4.4 },
      { name: '5xx', value: 2.3 },
    ],
    moduleActivity: [
      { name: 'flights', value: 45230 },
      { name: 'hotels', value: 32150 },
      { name: 'activities', value: 18920 },
      { name: 'loyalty', value: 15680 },
      { name: 'pointTransfer', value: 8950 },
      { name: 'common', value: 4500 },
    ],
    timeSeriesData: Array.from({ length: 24 }, (_, i) => ({
      timestamp: new Date(Date.now() - (23 - i) * 60 * 60 * 1000).toISOString(),
      value: Math.floor(Math.random() * 1000) + 500,
      errors: Math.floor(Math.random() * 50) + 10,
      responseTime: Math.floor(Math.random() * 500) + 800,
    })),
  };

  const displayMetrics = metrics?.data || mockMetrics;

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {Array.from({ length: 8 }).map((_, i) => (
          <Card key={i}>
            <CardContent className="p-6">
              <div className="animate-pulse">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-8 bg-gray-200 rounded w-1/2"></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-red-600 dark:text-red-400">
            Error loading dashboard: {error instanceof Error ? error.message : 'Unknown error'}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Logs</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                  {(displayMetrics?.totalLogs || 0).toLocaleString()}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Error Rate</p>
                <p className="text-2xl font-bold text-red-600 dark:text-red-400">
                  {displayMetrics?.errorRate || 0}%
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Avg Response Time</p>
                <p className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                  {formatDuration(displayMetrics?.avgResponseTime || 0)}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Active Users</p>
                <p className="text-2xl font-bold text-green-600 dark:text-green-400">
                  {(displayMetrics?.activeUsers || 0).toLocaleString()}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Charts Row 1 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Response Time Distribution */}
        <Card>
          <CardHeader>
            <CardTitle>Response Time Distribution</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={displayMetrics?.responseTimeDistribution || []}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {(displayMetrics?.responseTimeDistribution || []).map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Status Code Distribution */}
        <Card>
          <CardHeader>
            <CardTitle>Status Code Distribution</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={displayMetrics?.statusCodeDistribution || []}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="value" fill="#3B82F6" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Charts Row 2 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Module Activity */}
        <Card>
          <CardHeader>
            <CardTitle>Module Activity</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={displayMetrics?.moduleActivity || []} layout="horizontal">
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis type="number" />
                <YAxis dataKey="name" type="category" width={80} />
                <Tooltip />
                <Bar dataKey="value" fill="#10B981" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Top Errors */}
        <Card>
          <CardHeader>
            <CardTitle>Top Error Messages</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {(displayMetrics?.topErrors || []).map((error, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
                      {error.message}
                    </p>
                  </div>
                  <div className="ml-2 flex-shrink-0">
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
                      {error.count}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Time Series Chart */}
      <Card>
        <CardHeader>
          <CardTitle>Request Volume & Response Time (Last 24 Hours)</CardTitle>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={400}>
            <LineChart data={displayMetrics?.timeSeriesData || []}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis
                dataKey="timestamp"
                tickFormatter={(value) => new Date(value).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
              />
              <YAxis yAxisId="left" />
              <YAxis yAxisId="right" orientation="right" />
              <Tooltip
                labelFormatter={(value) => new Date(value).toLocaleString()}
                formatter={(value, name) => [
                  name === 'responseTime' ? `${value}ms` : value,
                  name === 'value' ? 'Requests' : name === 'errors' ? 'Errors' : 'Response Time'
                ]}
              />
              <Line yAxisId="left" type="monotone" dataKey="value" stroke="#3B82F6" strokeWidth={2} name="Requests" />
              <Line yAxisId="left" type="monotone" dataKey="errors" stroke="#EF4444" strokeWidth={2} name="Errors" />
              <Line yAxisId="right" type="monotone" dataKey="responseTime" stroke="#F59E0B" strokeWidth={2} name="Response Time" />
            </LineChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>
    </div>
  );
}
