import rateLimit from 'express-rate-limit';
import config from '../config/index.js';

export const createRateLimiter = (options?: {
  windowMs?: number;
  max?: number;
  message?: string;
}) => {
  return rateLimit({
    windowMs: options?.windowMs || config.rateLimitWindowMs,
    max: options?.max || config.rateLimitMaxRequests,
    message: {
      success: false,
      error: options?.message || 'Too many requests, please try again later',
      timestamp: new Date().toISOString()
    },
    standardHeaders: true,
    legacyHeaders: false,
  });
};

// Different rate limiters for different endpoints
export const generalRateLimit = createRateLimiter();

export const queryRateLimit = createRateLimiter({
  windowMs: 60 * 1000, // 1 minute
  max: 30, // 30 queries per minute
  message: 'Too many queries, please wait before making more requests'
});

export const uploadRateLimit = createRateLimiter({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // 5 uploads per 15 minutes
  message: 'Too many file uploads, please wait before uploading more files'
});

export const authRateLimit = createRateLimiter({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 10, // 10 login attempts per 15 minutes
  message: 'Too many authentication attempts, please try again later'
});
