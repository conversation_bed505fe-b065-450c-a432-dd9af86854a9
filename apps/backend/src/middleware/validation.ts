import { Request, Response, NextFunction } from 'express';
import <PERSON><PERSON> from 'joi';
import { createError } from './errorHandler.js';

export const validateBody = (schema: Joi.ObjectSchema) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    const { error, value } = schema.validate(req.body, {
      abortEarly: false,
      stripUnknown: true
    });

    if (error) {
      const errorMessage = error.details.map(detail => detail.message).join('; ');
      throw createError(`Validation error: ${errorMessage}`, 400);
    }

    req.body = value;
    next();
  };
};

export const validateQuery = (schema: Joi.ObjectSchema) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    const { error, value } = schema.validate(req.query, {
      abortEarly: false,
      stripUnknown: true
    });

    if (error) {
      const errorMessage = error.details.map(detail => detail.message).join('; ');
      throw createError(`Query validation error: ${errorMessage}`, 400);
    }

    req.query = value;
    next();
  };
};

// Common validation schemas
export const schemas = {
  logQuery: Joi.object({
    query: Joi.string().required().min(1).max(500),
    filters: Joi.object({
      level: Joi.string().valid('DEBUG', 'INFO', 'WARN', 'ERROR', 'FATAL'),
      module: Joi.string().valid('flights', 'hotels', 'activities', 'pointTransfer', 'loyalty', 'common'),
      status: Joi.array().items(Joi.number().min(100).max(599)),
      timeRange: Joi.object({
        start: Joi.string().isoDate().required(),
        end: Joi.string().isoDate().required()
      }),
      platform: Joi.string().valid('web', 'mobile', 'api'),
      customerId: Joi.string()
    }).optional(),
    limit: Joi.number().min(1).max(100).default(10),
    threshold: Joi.number().min(0).max(1).default(0.7)
  }),

  logAnalysis: Joi.object({
    logId: Joi.string().optional(),
    logEntry: Joi.object().optional(),
    analysisType: Joi.string().valid('single', 'rootCause', 'similarity', 'trend').required(),
    context: Joi.object({
      timeRange: Joi.string().optional(),
      relatedLogs: Joi.array().optional(),
      additionalContext: Joi.string().optional()
    }).optional()
  }),

  analytics: Joi.object({
    timeRange: Joi.object({
      start: Joi.string().isoDate().required(),
      end: Joi.string().isoDate().required()
    }).required(),
    modules: Joi.array().items(Joi.string()).optional(),
    groupBy: Joi.string().valid('hour', 'day', 'week').default('day'),
    metrics: Joi.array().items(Joi.string()).optional()
  }),

  auth: Joi.object({
    username: Joi.string().required().min(3).max(50),
    password: Joi.string().required().min(6).max(100)
  }),

  pagination: Joi.object({
    page: Joi.number().min(1).default(1),
    limit: Joi.number().min(1).max(100).default(20)
  })
};
