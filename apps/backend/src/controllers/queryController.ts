import { Request, Response } from 'express';
import { RAGService } from '../services/ragService.js';
import { LogQueryRequest, ApiResponse } from '../types/index.js';
import { createError } from '../middleware/errorHandler.js';
import config from '../config/index.js';

export class QueryController {
  private ragService: RAGService;

  constructor() {
    this.ragService = new RAGService({
      llmProvider: config.llmProvider,
      embeddingProvider: config.embeddingProvider,
      openaiApiKey: config.openaiApiKey,
      openaiModel: config.openaiModel,
      ollamaBaseUrl: config.ollamaBaseUrl,
      ollamaModel: config.ollamaModel,
      ollamaEmbeddingModel: config.ollamaEmbeddingModel,
      vectorDatabase: config.vectorDatabase,
      pineconeApiKey: config.pineconeApiKey,
      pineconeEnvironment: config.pineconeEnvironment,
      pineconeIndexName: config.pineconeIndexName,
      vectorDimension: config.vectorDimension,
    });
  }

  async initialize(): Promise<void> {
    await this.ragService.initialize();
  }

  /**
   * POST /api/query
   * Natural language log queries
   */
  queryLogs = async (req: Request, res: Response): Promise<void> => {
    try {
      const queryRequest: LogQueryRequest = req.body;
      
      const result = await this.ragService.queryLogs(queryRequest.query, {
        limit: queryRequest.limit,
        threshold: queryRequest.threshold,
      });

      const response: ApiResponse = {
        success: true,
        data: result,
        message: `Found ${result.relevantLogs.length} relevant log entries`,
        timestamp: new Date().toISOString()
      };

      res.json(response);
    } catch (error) {
      throw createError(
        `Query failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        500
      );
    }
  };

  /**
   * POST /api/query/similar
   * Find similar log entries
   */
  findSimilarLogs = async (req: Request, res: Response): Promise<void> => {
    try {
      const { logEntry, limit = 5, threshold = 0.8 } = req.body;

      if (!logEntry) {
        throw createError('Log entry is required', 400);
      }

      const similarLogs = await this.ragService.findSimilarLogs(logEntry, {
        limit,
        threshold,
      });

      const response: ApiResponse = {
        success: true,
        data: similarLogs,
        message: `Found ${similarLogs.length} similar log entries`,
        timestamp: new Date().toISOString()
      };

      res.json(response);
    } catch (error) {
      throw createError(
        `Similar logs search failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        500
      );
    }
  };

  /**
   * POST /api/query/translate
   * Translate natural language to structured filters
   */
  translateQuery = async (req: Request, res: Response): Promise<void> => {
    try {
      const { query } = req.body;

      if (!query) {
        throw createError('Query is required', 400);
      }

      // This would use the prompt engine to translate the query
      // For now, return a simple response
      const response: ApiResponse = {
        success: true,
        data: {
          originalQuery: query,
          translatedFilters: {},
          confidence: 0.8,
          suggestions: []
        },
        message: 'Query translated successfully',
        timestamp: new Date().toISOString()
      };

      res.json(response);
    } catch (error) {
      throw createError(
        `Query translation failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        500
      );
    }
  };

  /**
   * GET /api/query/stats
   * Get query service statistics
   */
  getStats = async (req: Request, res: Response): Promise<void> => {
    try {
      const stats = await this.ragService.getStats();

      const response: ApiResponse = {
        success: true,
        data: stats,
        timestamp: new Date().toISOString()
      };

      res.json(response);
    } catch (error) {
      throw createError(
        `Failed to get stats: ${error instanceof Error ? error.message : 'Unknown error'}`,
        500
      );
    }
  };

  async close(): Promise<void> {
    await this.ragService.close();
  }
}
