import { Request, Response } from 'express';
import { RAGService } from '../services/ragService.js';
import { LogAnalysisRequest, ApiResponse } from '../types/index.js';
import { createError } from '../middleware/errorHandler.js';
import config from '../config/index.js';

export class AnalysisController {
  private ragService: RAGService;

  constructor() {
    this.ragService = new RAGService({
      openaiApiKey: config.openaiApiKey,
      openaiModel: config.openaiModel,
      vectorDatabase: config.vectorDatabase,
      pineconeApiKey: config.pineconeApiKey,
      pineconeEnvironment: config.pineconeEnvironment,
      pineconeIndexName: config.pineconeIndexName,
      vectorDimension: config.vectorDimension,
    });
  }

  async initialize(): Promise<void> {
    await this.ragService.initialize();
  }

  /**
   * POST /api/analysis/single
   * Analyze a single log entry
   */
  analyzeSingleLog = async (req: Request, res: Response): Promise<void> => {
    try {
      const { logEntry } = req.body;

      if (!logEntry) {
        throw createError('Log entry is required', 400);
      }

      const analysis = await this.ragService.analyzeSingleLog(logEntry);

      const response: ApiResponse = {
        success: true,
        data: analysis,
        message: 'Log analysis completed',
        timestamp: new Date().toISOString()
      };

      res.json(response);
    } catch (error) {
      throw createError(
        `Single log analysis failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        500
      );
    }
  };

  /**
   * POST /api/analysis/root-cause
   * Perform root cause analysis on multiple logs
   */
  performRootCauseAnalysis = async (req: Request, res: Response): Promise<void> => {
    try {
      const { logEntries, timeRange, context } = req.body;

      if (!logEntries || !Array.isArray(logEntries) || logEntries.length === 0) {
        throw createError('Log entries array is required and must not be empty', 400);
      }

      if (!timeRange) {
        throw createError('Time range is required', 400);
      }

      const analysis = await this.ragService.performRootCauseAnalysis(
        logEntries,
        timeRange,
        context || ''
      );

      const response: ApiResponse = {
        success: true,
        data: analysis,
        message: 'Root cause analysis completed',
        timestamp: new Date().toISOString()
      };

      res.json(response);
    } catch (error) {
      throw createError(
        `Root cause analysis failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        500
      );
    }
  };

  /**
   * POST /api/analysis/trend
   * Analyze trends in log data
   */
  analyzeTrends = async (req: Request, res: Response): Promise<void> => {
    try {
      const { timeRange, modules, metrics } = req.body;

      if (!timeRange || !timeRange.start || !timeRange.end) {
        throw createError('Time range with start and end dates is required', 400);
      }

      // This would implement trend analysis
      // For now, return a placeholder response
      const trendAnalysis = {
        timeRange,
        trends: {
          responseTime: {
            average: 1250,
            trend: 'increasing',
            change: '+15%'
          },
          errorRate: {
            average: 2.3,
            trend: 'stable',
            change: '+0.1%'
          },
          requestVolume: {
            average: 1500,
            trend: 'increasing',
            change: '+25%'
          }
        },
        insights: [
          'Response times have increased by 15% over the selected period',
          'Error rates remain stable with minimal fluctuation',
          'Request volume shows significant growth, indicating increased usage'
        ],
        recommendations: [
          'Consider scaling infrastructure to handle increased load',
          'Investigate performance bottlenecks causing slower response times',
          'Monitor error rates closely as volume continues to grow'
        ]
      };

      const response: ApiResponse = {
        success: true,
        data: trendAnalysis,
        message: 'Trend analysis completed',
        timestamp: new Date().toISOString()
      };

      res.json(response);
    } catch (error) {
      throw createError(
        `Trend analysis failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        500
      );
    }
  };

  /**
   * POST /api/analysis/anomaly
   * Detect anomalies in log data
   */
  detectAnomalies = async (req: Request, res: Response): Promise<void> => {
    try {
      const { logEntry, historicalData } = req.body;

      if (!logEntry) {
        throw createError('Log entry is required', 400);
      }

      // This would implement anomaly detection
      // For now, return a placeholder response
      const anomalyDetection = {
        isAnomaly: false,
        confidence: 0.85,
        anomalyType: null,
        severity: 'LOW',
        explanation: 'Log entry appears normal compared to historical patterns',
        recommendations: []
      };

      const response: ApiResponse = {
        success: true,
        data: anomalyDetection,
        message: 'Anomaly detection completed',
        timestamp: new Date().toISOString()
      };

      res.json(response);
    } catch (error) {
      throw createError(
        `Anomaly detection failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        500
      );
    }
  };

  /**
   * POST /api/analysis/performance
   * Analyze performance metrics
   */
  analyzePerformance = async (req: Request, res: Response): Promise<void> => {
    try {
      const { timeRange, module, metrics } = req.body;

      if (!timeRange || !timeRange.start || !timeRange.end) {
        throw createError('Time range with start and end dates is required', 400);
      }

      // This would implement performance analysis
      // For now, return a placeholder response
      const performanceAnalysis = {
        timeRange,
        module: module || 'all',
        metrics: {
          averageResponseTime: 1250,
          p95ResponseTime: 3500,
          p99ResponseTime: 8000,
          throughput: 150,
          errorRate: 2.3,
          availability: 99.8
        },
        bottlenecks: [
          {
            type: 'database',
            impact: 'high',
            description: 'Database queries showing increased latency'
          }
        ],
        recommendations: [
          'Optimize database queries to reduce response times',
          'Consider implementing caching for frequently accessed data',
          'Monitor database connection pool utilization'
        ]
      };

      const response: ApiResponse = {
        success: true,
        data: performanceAnalysis,
        message: 'Performance analysis completed',
        timestamp: new Date().toISOString()
      };

      res.json(response);
    } catch (error) {
      throw createError(
        `Performance analysis failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        500
      );
    }
  };

  async close(): Promise<void> {
    await this.ragService.close();
  }
}
