import { Request, Response } from 'express';
import { LogParser, FileProcessor } from '@log-analyzer/parsers';
import { RAGService } from '../services/ragService.js';
import { UploadRequest, ApiResponse, LogProcessingJob } from '../types/index.js';
import { createError } from '../middleware/errorHandler.js';
import config from '../config/index.js';
import { v4 as uuidv4 } from 'uuid';

export class UploadController {
  private ragService: RAGService;
  private logParser: LogParser;
  private fileProcessor: FileProcessor;
  private processingJobs: Map<string, LogProcessingJob> = new Map();

  constructor() {
    this.ragService = new RAGService({
      llmProvider: config.llmProvider,
      embeddingProvider: config.embeddingProvider,
      openaiApiKey: config.openaiApiKey,
      openaiModel: config.openaiModel,
      ollamaBaseUrl: config.ollamaBaseUrl,
      ollamaModel: config.ollamaModel,
      ollamaEmbeddingModel: config.ollamaEmbeddingModel,
      vectorDatabase: config.vectorDatabase,
      pineconeApiKey: config.pineconeApiKey,
      pineconeEnvironment: config.pineconeEnvironment,
      pineconeIndexName: config.pineconeIndexName,
      vectorDimension: config.vectorDimension,
    });

    this.logParser = new LogParser({
      strictMode: false,
      skipMalformed: true,
      maxLineLength: 2000,
    });

    this.fileProcessor = new FileProcessor(this.logParser, {
      batchSize: config.logBatchSize,
      maxFileSize: config.maxFileSizeMB * 1024 * 1024,
      onProgress: this.onProcessingProgress.bind(this),
      onBatch: this.onBatchProcessed.bind(this),
    });
  }

  async initialize(): Promise<void> {
    await this.ragService.initialize();
  }

  /**
   * POST /api/upload
   * Upload and process log files
   */
  uploadFiles = async (req: Request, res: Response): Promise<void> => {
    try {
      const files = req.files as Express.Multer.File[];
      
      if (!files || files.length === 0) {
        throw createError('No files uploaded', 400);
      }

      const jobs: LogProcessingJob[] = [];

      // Create processing jobs for each file
      for (const file of files) {
        const jobId = uuidv4();
        const job: LogProcessingJob = {
          id: jobId,
          status: 'pending',
          sourceFile: file.originalname,
          totalLines: 0,
          processedLines: 0,
          errors: 0,
          startedAt: new Date(),
        };

        this.processingJobs.set(jobId, job);
        jobs.push(job);

        // Start processing asynchronously
        this.processFileAsync(jobId, file.path);
      }

      const response: ApiResponse = {
        success: true,
        data: {
          jobs: jobs.map(job => ({
            id: job.id,
            status: job.status,
            sourceFile: job.sourceFile,
          })),
        },
        message: `Started processing ${files.length} file(s)`,
        timestamp: new Date().toISOString()
      };

      res.json(response);
    } catch (error) {
      throw createError(
        `File upload failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        500
      );
    }
  };

  /**
   * GET /api/upload/jobs/:jobId
   * Get processing job status
   */
  getJobStatus = async (req: Request, res: Response): Promise<void> => {
    try {
      const { jobId } = req.params;
      const job = this.processingJobs.get(jobId);

      if (!job) {
        throw createError('Job not found', 404);
      }

      const response: ApiResponse = {
        success: true,
        data: job,
        timestamp: new Date().toISOString()
      };

      res.json(response);
    } catch (error) {
      throw createError(
        `Failed to get job status: ${error instanceof Error ? error.message : 'Unknown error'}`,
        500
      );
    }
  };

  /**
   * GET /api/upload/jobs
   * Get all processing jobs
   */
  getAllJobs = async (req: Request, res: Response): Promise<void> => {
    try {
      const jobs = Array.from(this.processingJobs.values());

      const response: ApiResponse = {
        success: true,
        data: jobs,
        timestamp: new Date().toISOString()
      };

      res.json(response);
    } catch (error) {
      throw createError(
        `Failed to get jobs: ${error instanceof Error ? error.message : 'Unknown error'}`,
        500
      );
    }
  };

  /**
   * POST /api/upload/local-path
   * Process files from local file system path
   */
  processLocalPath = async (req: Request, res: Response): Promise<void> => {
    try {
      const { filePath, options } = req.body;

      if (!filePath) {
        throw createError('File path is required', 400);
      }

      // Import fs modules
      const fs = await import('fs');
      const path = await import('path');

      // Check if path exists
      if (!fs.existsSync(filePath)) {
        throw createError('File or directory does not exist', 400);
      }

      const jobs: LogProcessingJob[] = [];
      const stats = fs.statSync(filePath);

      if (stats.isFile()) {
        // Single file
        const jobId = uuidv4();
        const job: LogProcessingJob = {
          id: jobId,
          status: 'pending',
          sourceFile: path.basename(filePath),
          totalLines: 0,
          processedLines: 0,
          errors: 0,
          startedAt: new Date(),
        };

        this.processingJobs.set(jobId, job);
        jobs.push(job);

        // Start processing asynchronously
        this.processFileAsync(jobId, filePath);

      } else if (stats.isDirectory()) {
        // Directory - process all log files (including date extensions)
        const files = fs.readdirSync(filePath)
          .filter(file => {
            // Match .log, .txt files and files with date extensions like .log.2025-07-14
            return file.match(/\.(log|txt)(\.\d{4}-\d{2}-\d{2})?$/i);
          })
          .map(file => path.join(filePath, file));

        if (files.length === 0) {
          throw createError('No log files found in directory', 400);
        }

        for (const file of files) {
          const jobId = uuidv4();
          const job: LogProcessingJob = {
            id: jobId,
            status: 'pending',
            sourceFile: path.basename(file),
            totalLines: 0,
            processedLines: 0,
            errors: 0,
            startedAt: new Date(),
          };

          this.processingJobs.set(jobId, job);
          jobs.push(job);

          // Start processing asynchronously
          this.processFileAsync(jobId, file);
        }
      } else {
        throw createError('Path must be a file or directory', 400);
      }

      const response: ApiResponse = {
        success: true,
        data: {
          jobs: jobs.map(job => ({
            id: job.id,
            status: job.status,
            sourceFile: job.sourceFile,
          })),
        },
        message: `Started processing ${jobs.length} file(s) from local path`,
        timestamp: new Date().toISOString()
      };

      res.json(response);
    } catch (error) {
      throw createError(
        `Failed to process local path: ${error instanceof Error ? error.message : 'Unknown error'}`,
        500
      );
    }
  };

  /**
   * DELETE /api/upload/jobs/:jobId
   * Cancel a processing job
   */
  cancelJob = async (req: Request, res: Response): Promise<void> => {
    try {
      const { jobId } = req.params;
      const job = this.processingJobs.get(jobId);

      if (!job) {
        throw createError('Job not found', 404);
      }

      if (job.status === 'completed' || job.status === 'failed') {
        throw createError('Cannot cancel completed or failed job', 400);
      }

      job.status = 'failed';
      job.error = 'Cancelled by user';
      job.completedAt = new Date();

      const response: ApiResponse = {
        success: true,
        data: job,
        message: 'Job cancelled successfully',
        timestamp: new Date().toISOString()
      };

      res.json(response);
    } catch (error) {
      throw createError(
        `Failed to cancel job: ${error instanceof Error ? error.message : 'Unknown error'}`,
        500
      );
    }
  };

  private async processFileAsync(jobId: string, filePath: string): Promise<void> {
    const job = this.processingJobs.get(jobId);
    if (!job) return;

    try {
      job.status = 'processing';

      const result = await this.fileProcessor.processFile(filePath);
      
      job.totalLines = result.totalLines;
      job.processedLines = result.successfullyParsed;
      job.errors = result.failed;

      if (result.entries.length > 0) {
        // Index the parsed log entries
        await this.ragService.indexLogEntries(result.entries);
      }

      job.status = 'completed';
      job.completedAt = new Date();

      console.log(`Job ${jobId} completed: ${result.successfullyParsed}/${result.totalLines} entries processed`);

    } catch (error) {
      job.status = 'failed';
      job.error = error instanceof Error ? error.message : 'Unknown error';
      job.completedAt = new Date();

      console.error(`Job ${jobId} failed:`, error);
    }
  }

  private onProcessingProgress(processed: number, total: number): void {
    // Update progress for active jobs
    // This is a simplified implementation
    console.log(`Processing progress: ${processed}/${total}`);
  }

  private async onBatchProcessed(batch: any[]): Promise<void> {
    // This is called when each batch is processed
    // Could be used for real-time updates or streaming
    console.log(`Batch processed: ${batch.length} entries`);
  }

  async close(): Promise<void> {
    await this.ragService.close();
  }
}
