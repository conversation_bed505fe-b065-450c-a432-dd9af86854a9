export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  timestamp: string;
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export interface LogQueryRequest {
  query: string;
  filters?: {
    level?: string;
    module?: string;
    status?: number[];
    timeRange?: {
      start: string;
      end: string;
    };
    platform?: string;
    customerId?: string;
  };
  limit?: number;
  threshold?: number;
}

export interface LogAnalysisRequest {
  logId?: string;
  logEntry?: any;
  analysisType: 'single' | 'rootCause' | 'similarity' | 'trend';
  context?: {
    timeRange?: string;
    relatedLogs?: any[];
    additionalContext?: string;
  };
}

export interface UploadRequest {
  files: Express.Multer.File[];
  options?: {
    skipMalformed?: boolean;
    batchSize?: number;
  };
}

export interface AnalyticsRequest {
  timeRange: {
    start: string;
    end: string;
  };
  modules?: string[];
  groupBy?: 'hour' | 'day' | 'week';
  metrics?: string[];
}

export interface User {
  id: string;
  username: string;
  email: string;
  role: 'admin' | 'analyst' | 'viewer';
  createdAt: Date;
  lastLogin?: Date;
}

export interface AuthRequest {
  username: string;
  password: string;
}

export interface JWTPayload {
  userId: string;
  username: string;
  role: string;
  iat: number;
  exp: number;
}

export interface LogProcessingJob {
  id: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  sourceFile: string;
  totalLines: number;
  processedLines: number;
  errors: number;
  startedAt: Date;
  completedAt?: Date;
  error?: string;
}

export interface SystemHealth {
  status: 'healthy' | 'degraded' | 'unhealthy';
  services: {
    database: boolean;
    vectorDatabase: boolean;
    llm: boolean;
    redis?: boolean;
  };
  metrics: {
    uptime: number;
    memoryUsage: number;
    cpuUsage: number;
    diskUsage: number;
  };
  lastCheck: Date;
}
