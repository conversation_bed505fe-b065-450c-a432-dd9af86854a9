import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import { createServer } from 'http';
import config from './config/index.js';
import { errorHandler, notFoundHandler } from './middleware/errorHandler.js';
import { generalRateLimit } from './middleware/rateLimiter.js';
import { ApiResponse, SystemHealth } from './types/index.js';

// Import routes
import authRoutes from './routes/auth.js';
import queryRoutes from './routes/query.js';
import analysisRoutes from './routes/analysis.js';
import uploadRoutes from './routes/upload.js';

const app = express();
const server = createServer(app);

// Security middleware
app.use(helmet());
app.use(cors({
  origin: config.corsOrigins,
  credentials: true,
}));

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Rate limiting
app.use(generalRateLimit);

// Health check endpoint
app.get('/health', (req, res) => {
  const health: SystemHealth = {
    status: 'healthy',
    services: {
      database: true,
      vectorDatabase: true,
      llm: true,
    },
    metrics: {
      uptime: process.uptime(),
      memoryUsage: process.memoryUsage().heapUsed / 1024 / 1024, // MB
      cpuUsage: process.cpuUsage().user / 1000000, // Convert to seconds
      diskUsage: 0, // Would need additional implementation
    },
    lastCheck: new Date()
  };

  const response: ApiResponse<SystemHealth> = {
    success: true,
    data: health,
    timestamp: new Date().toISOString()
  };

  res.json(response);
});

// API routes
app.use('/api/auth', authRoutes);
app.use('/api/query', queryRoutes);
app.use('/api/analysis', analysisRoutes);
app.use('/api/upload', uploadRoutes);

// Analytics endpoint
app.get('/api/analytics', (req, res) => {
  // This would implement analytics functionality
  const response: ApiResponse = {
    success: true,
    data: {
      message: 'Analytics endpoint - implementation pending',
      availableMetrics: [
        'response_time_distribution',
        'status_code_breakdown',
        'error_trends',
        'module_activity',
        'platform_usage'
      ]
    },
    timestamp: new Date().toISOString()
  };

  res.json(response);
});

// Logs endpoint for basic log retrieval
app.get('/api/logs', (req, res) => {
  // This would implement log retrieval functionality
  const response: ApiResponse = {
    success: true,
    data: {
      logs: [],
      pagination: {
        page: 1,
        limit: 20,
        total: 0,
        totalPages: 0
      }
    },
    message: 'Log retrieval endpoint - implementation pending',
    timestamp: new Date().toISOString()
  };

  res.json(response);
});

// Root endpoint
app.get('/', (req, res) => {
  const response: ApiResponse = {
    success: true,
    data: {
      name: 'Log Analyzer RAG API',
      version: '1.0.0',
      description: 'RAG-powered log analysis application backend',
      endpoints: {
        auth: '/api/auth',
        query: '/api/query',
        analysis: '/api/analysis',
        upload: '/api/upload',
        analytics: '/api/analytics',
        logs: '/api/logs',
        health: '/health'
      }
    },
    timestamp: new Date().toISOString()
  };

  res.json(response);
});

// Error handling middleware (must be last)
app.use(notFoundHandler);
app.use(errorHandler);

// Graceful shutdown handling
const gracefulShutdown = (signal: string) => {
  console.log(`Received ${signal}. Starting graceful shutdown...`);
  
  server.close(() => {
    console.log('HTTP server closed');
    process.exit(0);
  });

  // Force close after 30 seconds
  setTimeout(() => {
    console.error('Could not close connections in time, forcefully shutting down');
    process.exit(1);
  }, 30000);
};

process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));

// Start server
server.listen(config.port, () => {
  console.log(`🚀 Server running on port ${config.port}`);
  console.log(`📊 Environment: ${config.nodeEnv}`);
  console.log(`🔍 Vector Database: ${config.vectorDatabase}`);
  console.log(`🤖 OpenAI Model: ${config.openaiModel}`);
  console.log(`📁 Max File Size: ${config.maxFileSizeMB}MB`);
  console.log(`⚡ Batch Size: ${config.logBatchSize}`);
});

export default app;
