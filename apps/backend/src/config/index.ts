import dotenv from 'dotenv';
import { join } from 'path';

// Load environment variables from the root directory
dotenv.config({ path: join(process.cwd(), '../../.env') });

export interface AppConfig {
  port: number;
  nodeEnv: string;
  corsOrigins: string[];

  // Database
  redisUrl?: string;
  redisPassword?: string;

  // LLM Configuration
  llmProvider: 'openai' | 'ollama';
  embeddingProvider: 'openai' | 'ollama';

  // OpenAI
  openaiApiKey?: string;
  openaiModel: string;

  // Ollama
  ollamaBaseUrl: string;
  ollamaModel: string;
  ollamaEmbeddingModel: string;

  // Vector Database
  vectorDatabase: 'faiss' | 'pinecone';
  pineconeApiKey?: string;
  pineconeEnvironment?: string;
  pineconeIndexName?: string;
  vectorDimension: number;
  similarityThreshold: number;
  
  // Authentication
  jwtSecret: string;
  jwtExpiresIn: string;
  
  // Rate Limiting
  rateLimitWindowMs: number;
  rateLimitMaxRequests: number;
  
  // File Processing
  logBatchSize: number;
  maxFileSizeMB: number;
  
  // Monitoring
  enableMetrics: boolean;
  healthCheckInterval: number;
}

function validateConfig(): AppConfig {
  const llmProvider = (process.env.LLM_PROVIDER as 'openai' | 'ollama') || 'ollama';
  const embeddingProvider = (process.env.EMBEDDING_PROVIDER as 'openai' | 'ollama') || 'ollama';

  const requiredEnvVars = ['JWT_SECRET'];

  // Add provider-specific required variables
  if (llmProvider === 'openai' || embeddingProvider === 'openai') {
    requiredEnvVars.push('OPENAI_API_KEY');
  }

  const missing = requiredEnvVars.filter(envVar => !process.env[envVar]);
  if (missing.length > 0) {
    throw new Error(`Missing required environment variables: ${missing.join(', ')}`);
  }

  const vectorDatabase = (process.env.VECTOR_DATABASE as 'faiss' | 'pinecone') || 'faiss';
  
  if (vectorDatabase === 'pinecone') {
    if (!process.env.PINECONE_API_KEY || !process.env.PINECONE_INDEX_NAME) {
      throw new Error('PINECONE_API_KEY and PINECONE_INDEX_NAME are required when using Pinecone');
    }
  }

  return {
    port: parseInt(process.env.PORT || '3001', 10),
    nodeEnv: process.env.NODE_ENV || 'development',
    corsOrigins: process.env.CORS_ORIGINS?.split(',') || ['http://localhost:3000'],

    // Database
    redisUrl: process.env.REDIS_URL,
    redisPassword: process.env.REDIS_PASSWORD,

    // LLM Configuration
    llmProvider,
    embeddingProvider,

    // OpenAI
    openaiApiKey: process.env.OPENAI_API_KEY,
    openaiModel: process.env.OPENAI_MODEL || 'gpt-4',

    // Ollama
    ollamaBaseUrl: process.env.OLLAMA_BASE_URL || 'http://localhost:11434',
    ollamaModel: process.env.OLLAMA_MODEL || 'llama2',
    ollamaEmbeddingModel: process.env.OLLAMA_EMBEDDING_MODEL || 'nomic-embed-text',
    
    // Vector Database
    vectorDatabase,
    pineconeApiKey: process.env.PINECONE_API_KEY,
    pineconeEnvironment: process.env.PINECONE_ENVIRONMENT,
    pineconeIndexName: process.env.PINECONE_INDEX_NAME,
    vectorDimension: parseInt(process.env.VECTOR_DIMENSION || '1536', 10),
    similarityThreshold: parseFloat(process.env.SIMILARITY_THRESHOLD || '0.7'),
    
    // Authentication
    jwtSecret: process.env.JWT_SECRET!,
    jwtExpiresIn: process.env.JWT_EXPIRES_IN || '24h',
    
    // Rate Limiting
    rateLimitWindowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000', 10), // 15 minutes
    rateLimitMaxRequests: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100', 10),
    
    // File Processing
    logBatchSize: parseInt(process.env.LOG_BATCH_SIZE || '1000', 10),
    maxFileSizeMB: parseInt(process.env.MAX_FILE_SIZE_MB || '100', 10),
    
    // Monitoring
    enableMetrics: process.env.ENABLE_METRICS === 'true',
    healthCheckInterval: parseInt(process.env.HEALTH_CHECK_INTERVAL || '30000', 10),
  };
}

export const config = validateConfig();

export default config;
