import OpenAI from 'openai';
import { 
  EmbeddingService, 
  OpenAIEmbeddingProvider, 
  FAISSVectorDatabase, 
  PineconeVectorDatabase,
  SearchResult 
} from '@log-analyzer/embeddings';
import { PromptEngine } from '@log-analyzer/prompts';
import { LogParser, ParsedLogEntry } from '@log-analyzer/parsers';

export interface RAGConfig {
  openaiApiKey: string;
  openaiModel?: string;
  vectorDatabase: 'faiss' | 'pinecone';
  pineconeApiKey?: string;
  pineconeEnvironment?: string;
  pineconeIndexName?: string;
  vectorDimension?: number;
}

export interface QueryResult {
  answer: string;
  relevantLogs: SearchResult[];
  confidence: number;
  processingTime: number;
  sources: string[];
}

export interface AnalysisResult {
  analysis: string;
  insights: string[];
  recommendations: string[];
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  confidence: number;
}

export class RAGService {
  private embeddingService: EmbeddingService;
  private promptEngine: PromptEngine;
  private openaiClient: OpenAI;
  private logParser: LogParser;
  private isInitialized = false;

  constructor(private config: RAGConfig) {
    // Initialize OpenAI client
    this.openaiClient = new OpenAI({
      apiKey: config.openaiApiKey,
    });

    // Initialize prompt engine
    this.promptEngine = new PromptEngine();

    // Initialize log parser
    this.logParser = new LogParser();

    // Initialize embedding service
    this.initializeEmbeddingService();
  }

  private initializeEmbeddingService(): void {
    // Create embedding provider
    const embeddingProvider = new OpenAIEmbeddingProvider({
      provider: 'openai',
      model: 'text-embedding-3-small',
      apiKey: this.config.openaiApiKey,
    });

    // Create vector database
    let vectorDatabase;
    if (this.config.vectorDatabase === 'pinecone') {
      if (!this.config.pineconeApiKey || !this.config.pineconeIndexName) {
        throw new Error('Pinecone API key and index name are required for Pinecone database');
      }
      vectorDatabase = new PineconeVectorDatabase({
        provider: 'pinecone',
        dimension: this.config.vectorDimension || 1536,
        apiKey: this.config.pineconeApiKey,
        environment: this.config.pineconeEnvironment,
        indexName: this.config.pineconeIndexName,
      });
    } else {
      vectorDatabase = new FAISSVectorDatabase({
        provider: 'faiss',
        dimension: this.config.vectorDimension || 1536,
        indexName: 'logs',
      });
    }

    this.embeddingService = new EmbeddingService(embeddingProvider, vectorDatabase);
  }

  async initialize(): Promise<void> {
    try {
      await this.embeddingService.initialize();
      this.isInitialized = true;
      console.log('RAG service initialized successfully');
    } catch (error) {
      throw new Error(`Failed to initialize RAG service: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Process and index log entries for RAG
   */
  async indexLogEntries(logEntries: ParsedLogEntry[]): Promise<void> {
    if (!this.isInitialized) {
      throw new Error('RAG service not initialized');
    }

    try {
      const result = await this.embeddingService.processLogEntries(logEntries);
      console.log(`Indexed ${result.successful} log entries, ${result.failed} failed`);
      
      if (result.failed > 0) {
        console.warn('Some log entries failed to index:', result.errors);
      }
    } catch (error) {
      throw new Error(`Failed to index log entries: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Query logs using natural language
   */
  async queryLogs(query: string, options: { limit?: number; threshold?: number } = {}): Promise<QueryResult> {
    if (!this.isInitialized) {
      throw new Error('RAG service not initialized');
    }

    const startTime = Date.now();

    try {
      // First, try to translate the query to structured filters
      const queryTranslation = this.promptEngine.processQueryTranslation(query);
      
      // Search for relevant logs
      const relevantLogs = await this.embeddingService.searchLogs(query, {
        limit: options.limit || 10,
        threshold: options.threshold || 0.7,
      });

      if (relevantLogs.length === 0) {
        return {
          answer: "I couldn't find any relevant log entries for your query. Please try rephrasing your question or check if the logs have been indexed.",
          relevantLogs: [],
          confidence: 0,
          processingTime: Date.now() - startTime,
          sources: []
        };
      }

      // Generate answer using LLM
      const answer = await this.generateAnswer(query, relevantLogs);
      
      const processingTime = Date.now() - startTime;
      const sources = relevantLogs.map(log => log.metadata.sourceFile || 'unknown').filter(Boolean);

      return {
        answer,
        relevantLogs,
        confidence: this.calculateConfidence(relevantLogs),
        processingTime,
        sources: [...new Set(sources)] // Remove duplicates
      };

    } catch (error) {
      throw new Error(`Failed to query logs: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Analyze a single log entry
   */
  async analyzeSingleLog(logEntry: ParsedLogEntry): Promise<AnalysisResult> {
    if (!this.isInitialized) {
      throw new Error('RAG service not initialized');
    }

    try {
      // Generate prompt for single log analysis
      const processedPrompt = this.promptEngine.processSingleLogAnalysis(logEntry);
      
      // Get LLM analysis
      const response = await this.openaiClient.chat.completions.create({
        model: this.config.openaiModel || 'gpt-4',
        messages: [
          {
            role: 'system',
            content: 'You are an expert log analyst. Provide detailed, actionable insights about log entries.'
          },
          {
            role: 'user',
            content: processedPrompt.prompt
          }
        ],
        temperature: 0.1,
        max_tokens: 1500,
      });

      const analysis = response.choices[0]?.message?.content || 'No analysis available';
      
      // Parse the analysis to extract structured information
      const { insights, recommendations, severity } = this.parseAnalysisResponse(analysis);

      return {
        analysis,
        insights,
        recommendations,
        severity,
        confidence: 0.8 // Base confidence for single log analysis
      };

    } catch (error) {
      throw new Error(`Failed to analyze log entry: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Perform root cause analysis on multiple related logs
   */
  async performRootCauseAnalysis(
    logEntries: ParsedLogEntry[], 
    timeRange: string, 
    context: string = ''
  ): Promise<AnalysisResult> {
    if (!this.isInitialized) {
      throw new Error('RAG service not initialized');
    }

    try {
      const modules = [...new Set(logEntries.map(log => log.module))];
      
      // Generate prompt for root cause analysis
      const processedPrompt = this.promptEngine.processRootCauseAnalysis(
        logEntries, 
        timeRange, 
        modules, 
        context
      );
      
      // Get LLM analysis
      const response = await this.openaiClient.chat.completions.create({
        model: this.config.openaiModel || 'gpt-4',
        messages: [
          {
            role: 'system',
            content: 'You are an expert system analyst specializing in root cause analysis. Provide comprehensive, actionable insights.'
          },
          {
            role: 'user',
            content: processedPrompt.prompt
          }
        ],
        temperature: 0.1,
        max_tokens: 2000,
      });

      const analysis = response.choices[0]?.message?.content || 'No analysis available';
      
      // Parse the analysis to extract structured information
      const { insights, recommendations, severity } = this.parseAnalysisResponse(analysis);

      return {
        analysis,
        insights,
        recommendations,
        severity,
        confidence: this.calculateAnalysisConfidence(logEntries.length)
      };

    } catch (error) {
      throw new Error(`Failed to perform root cause analysis: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Find similar log entries
   */
  async findSimilarLogs(logEntry: ParsedLogEntry, options: { limit?: number; threshold?: number } = {}): Promise<SearchResult[]> {
    if (!this.isInitialized) {
      throw new Error('RAG service not initialized');
    }

    try {
      return await this.embeddingService.findSimilarLogs(logEntry, {
        limit: options.limit || 5,
        threshold: options.threshold || 0.8,
      });
    } catch (error) {
      throw new Error(`Failed to find similar logs: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get service statistics
   */
  async getStats() {
    if (!this.isInitialized) {
      throw new Error('RAG service not initialized');
    }

    return await this.embeddingService.getStats();
  }

  /**
   * Close the service
   */
  async close(): Promise<void> {
    if (this.isInitialized) {
      await this.embeddingService.close();
      this.isInitialized = false;
      console.log('RAG service closed');
    }
  }

  private async generateAnswer(query: string, relevantLogs: SearchResult[]): Promise<string> {
    const context = relevantLogs.map((log, index) => {
      return `Log ${index + 1}:
- Timestamp: ${log.metadata.timestamp}
- Level: ${log.metadata.level}
- Module: ${log.metadata.module}
- Status: ${log.metadata.status}
- Response Time: ${log.metadata.responseTime}ms
- Text: ${log.text}
- Score: ${log.score.toFixed(3)}`;
    }).join('\n\n');

    const prompt = `Based on the following log entries, please answer the user's question: "${query}"

Relevant Log Entries:
${context}

Please provide a comprehensive answer that:
1. Directly addresses the user's question
2. References specific log entries when relevant
3. Provides actionable insights
4. Explains any patterns or anomalies found
5. Suggests next steps if appropriate

Answer:`;

    const response = await this.openaiClient.chat.completions.create({
      model: this.config.openaiModel || 'gpt-4',
      messages: [
        {
          role: 'system',
          content: 'You are an expert log analyst. Provide clear, actionable answers based on log data.'
        },
        {
          role: 'user',
          content: prompt
        }
      ],
      temperature: 0.1,
      max_tokens: 1000,
    });

    return response.choices[0]?.message?.content || 'Unable to generate answer';
  }

  private calculateConfidence(relevantLogs: SearchResult[]): number {
    if (relevantLogs.length === 0) return 0;
    
    const avgScore = relevantLogs.reduce((sum, log) => sum + log.score, 0) / relevantLogs.length;
    const logCount = Math.min(relevantLogs.length / 10, 1); // More logs = higher confidence, capped at 1
    
    return Math.min(avgScore * logCount, 1);
  }

  private calculateAnalysisConfidence(logCount: number): number {
    // More logs generally provide better analysis confidence
    if (logCount >= 10) return 0.9;
    if (logCount >= 5) return 0.8;
    if (logCount >= 3) return 0.7;
    if (logCount >= 1) return 0.6;
    return 0.3;
  }

  private parseAnalysisResponse(analysis: string): {
    insights: string[];
    recommendations: string[];
    severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  } {
    const insights: string[] = [];
    const recommendations: string[] = [];
    let severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL' = 'LOW';

    // Simple parsing - in a real implementation, you might use more sophisticated NLP
    const lines = analysis.split('\n');
    
    for (const line of lines) {
      const trimmed = line.trim();
      if (trimmed.includes('insight') || trimmed.includes('finding')) {
        insights.push(trimmed);
      }
      if (trimmed.includes('recommend') || trimmed.includes('suggest')) {
        recommendations.push(trimmed);
      }
      if (trimmed.toLowerCase().includes('critical')) {
        severity = 'CRITICAL';
      } else if (trimmed.toLowerCase().includes('high') && severity !== 'CRITICAL') {
        severity = 'HIGH';
      } else if (trimmed.toLowerCase().includes('medium') && !['CRITICAL', 'HIGH'].includes(severity)) {
        severity = 'MEDIUM';
      }
    }

    return { insights, recommendations, severity };
  }
}
