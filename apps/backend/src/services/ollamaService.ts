import axios from 'axios';

export interface OllamaConfig {
  baseUrl?: string;
  model: string;
  temperature?: number;
  maxTokens?: number;
}

export interface OllamaMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
}

export interface OllamaResponse {
  message: {
    role: string;
    content: string;
  };
  done: boolean;
}

export class OllamaService {
  private baseUrl: string;
  private model: string;
  private temperature: number;
  private maxTokens: number;
  private isInitialized = false;

  constructor(config: OllamaConfig) {
    this.baseUrl = config.baseUrl || 'http://localhost:11434';
    this.model = config.model || 'llama2';
    this.temperature = config.temperature || 0.1;
    this.maxTokens = config.maxTokens || 2000;
  }

  async initialize(): Promise<void> {
    try {
      // Check if Ollama is available
      await axios.get(`${this.baseUrl}/api/version`);

      // Check if the model is available
      const modelsResponse = await axios.get(`${this.baseUrl}/api/tags`);
      const availableModels = modelsResponse.data.models?.map((m: any) => m.name) || [];

      if (!availableModels.some((name: string) => name.includes(this.model))) {
        console.log(`Model ${this.model} not found. Attempting to pull...`);
        try {
          await this.pullModel();
          console.log(`Model ${this.model} pulled successfully`);
        } catch (pullError) {
          console.warn(`Failed to pull model ${this.model}:`, pullError);
          throw new Error(`Model ${this.model} not available and failed to pull. Please run: ollama pull ${this.model}`);
        }
      }

      this.isInitialized = true;
      console.log(`Ollama service initialized with model: ${this.model}`);
    } catch (error) {
      throw new Error(`Failed to initialize Ollama service: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async chat(messages: OllamaMessage[]): Promise<string> {
    if (!this.isInitialized) {
      throw new Error('Ollama service not initialized');
    }

    try {
      const response = await axios.post(`${this.baseUrl}/api/chat`, {
        model: this.model,
        messages: messages,
        stream: false,
        options: {
          temperature: this.temperature,
          num_predict: this.maxTokens,
        }
      });

      if (!response.data.message?.content) {
        throw new Error('Invalid response from Ollama API');
      }

      return response.data.message.content;
    } catch (error) {
      if (axios.isAxiosError(error)) {
        if (error.response?.status === 404) {
          throw new Error(`Model ${this.model} not found. Please run: ollama pull ${this.model}`);
        }
        throw new Error(`Ollama API error: ${error.response?.data?.error || error.message}`);
      }
      throw new Error(`Failed to generate response: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async generate(prompt: string, systemPrompt?: string): Promise<string> {
    const messages: OllamaMessage[] = [];
    
    if (systemPrompt) {
      messages.push({ role: 'system', content: systemPrompt });
    }
    
    messages.push({ role: 'user', content: prompt });
    
    return await this.chat(messages);
  }

  async isModelAvailable(): Promise<boolean> {
    try {
      const modelsResponse = await axios.get(`${this.baseUrl}/api/tags`);
      const availableModels = modelsResponse.data.models?.map((m: any) => m.name) || [];
      return availableModels.some((name: string) => name.includes(this.model));
    } catch (error) {
      return false;
    }
  }

  async pullModel(): Promise<void> {
    try {
      console.log(`Pulling model ${this.model}...`);
      await axios.post(`${this.baseUrl}/api/pull`, {
        name: this.model
      });
      console.log(`Model ${this.model} pulled successfully`);
    } catch (error) {
      throw new Error(`Failed to pull model: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  getAvailableModels = async (): Promise<string[]> => {
    try {
      const response = await axios.get(`${this.baseUrl}/api/tags`);
      return response.data.models?.map((m: any) => m.name) || [];
    } catch (error) {
      throw new Error(`Failed to get available models: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };
}
