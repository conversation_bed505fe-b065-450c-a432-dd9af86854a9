import { Router } from 'express';
import { AnalysisController } from '../controllers/analysisController.js';
import { authenticateToken, requireRole } from '../middleware/auth.js';
import { queryRateLimit } from '../middleware/rateLimiter.js';
import { validateBody } from '../middleware/validation.js';
import { schemas } from '../middleware/validation.js';
import { asyncHandler } from '../middleware/errorHandler.js';

const router = Router();
const analysisController = new AnalysisController();

// Initialize the controller asynchronously
analysisController.initialize().catch(console.error);

// Apply authentication and rate limiting to all routes
router.use(authenticateToken);
router.use(queryRateLimit);

/**
 * POST /api/analysis/single
 * Analyze a single log entry
 */
router.post(
  '/single',
  requireRole(['admin', 'analyst']),
  asyncHand<PERSON>(analysisController.analyzeSingleLog)
);

/**
 * POST /api/analysis/root-cause
 * Perform root cause analysis on multiple logs
 */
router.post(
  '/root-cause',
  requireR<PERSON>(['admin', 'analyst']),
  async<PERSON>and<PERSON>(analysisController.performRootCauseAnalysis)
);

/**
 * POST /api/analysis/trend
 * Analyze trends in log data
 */
router.post(
  '/trend',
  requireRole(['admin', 'analyst']),
  validateBody(schemas.analytics),
  asyncHandler(analysisController.analyzeTrends)
);

/**
 * POST /api/analysis/anomaly
 * Detect anomalies in log data
 */
router.post(
  '/anomaly',
  requireRole(['admin', 'analyst']),
  asyncHandler(analysisController.detectAnomalies)
);

/**
 * POST /api/analysis/performance
 * Analyze performance metrics
 */
router.post(
  '/performance',
  requireRole(['admin', 'analyst']),
  asyncHandler(analysisController.analyzePerformance)
);

export default router;
