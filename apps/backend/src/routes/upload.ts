import { Router } from 'express';
import multer from 'multer';
import { UploadController } from '../controllers/uploadController.js';
import { authenticateToken, requireRole } from '../middleware/auth.js';
import { uploadRateLimit } from '../middleware/rateLimiter.js';
import { asyncHandler } from '../middleware/errorHandler.js';
import config from '../config/index.js';

const router = Router();
const uploadController = new UploadController();

// Initialize the controller asynchronously
uploadController.initialize().catch(console.error);

// Configure multer for file uploads
const upload = multer({
  dest: 'uploads/',
  limits: {
    fileSize: config.maxFileSizeMB * 1024 * 1024, // Convert MB to bytes
    files: 20, // Maximum 20 files per upload
  },
  fileFilter: (req, file, cb) => {
    // Accept log files and text files (including date extensions)
    const allowedMimes = [
      'text/plain',
      'application/octet-stream',
      'text/x-log'
    ];

    // Match .log, .txt files and files with date extensions like .log.2025-07-14
    const logFilePattern = /\.(log|txt)(\.\d{4}-\d{2}-\d{2})?$/i;
    const hasValidExtension = logFilePattern.test(file.originalname.toLowerCase());

    if (allowedMimes.includes(file.mimetype) || hasValidExtension) {
      cb(null, true);
    } else {
      cb(new Error('Only log files (.log, .txt, .log.YYYY-MM-DD) are allowed'));
    }
  }
});

// Apply authentication and rate limiting to all routes
router.use(authenticateToken);

/**
 * POST /api/upload
 * Upload and process log files
 */
router.post(
  '/',
  requireRole(['admin', 'analyst']),
  uploadRateLimit,
  upload.array('files', 10),
  asyncHandler(uploadController.uploadFiles)
);

/**
 * POST /api/upload/local-path
 * Process files from local file system path
 */
router.post(
  '/local-path',
  requireRole(['admin', 'analyst']),
  uploadRateLimit,
  asyncHandler(uploadController.processLocalPath)
);

/**
 * GET /api/upload/jobs/:jobId
 * Get processing job status
 */
router.get(
  '/jobs/:jobId',
  requireRole(['admin', 'analyst', 'viewer']),
  asyncHandler(uploadController.getJobStatus)
);

/**
 * GET /api/upload/jobs
 * Get all processing jobs
 */
router.get(
  '/jobs',
  requireRole(['admin', 'analyst']),
  asyncHandler(uploadController.getAllJobs)
);

/**
 * DELETE /api/upload/jobs/:jobId
 * Cancel a processing job
 */
router.delete(
  '/jobs/:jobId',
  requireRole(['admin', 'analyst']),
  asyncHandler(uploadController.cancelJob)
);

export default router;
