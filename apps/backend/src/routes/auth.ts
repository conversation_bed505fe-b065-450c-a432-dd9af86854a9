import { Router } from 'express';
import bcrypt from 'bcryptjs';
import { generateToken, authenticateToken } from '../middleware/auth.js';
import { authRateLimit } from '../middleware/rateLimiter.js';
import { validateBody } from '../middleware/validation.js';
import { schemas } from '../middleware/validation.js';
import { asyncHandler, createError } from '../middleware/errorHandler.js';
import { ApiResponse, User } from '../types/index.js';

const router = Router();

// Mock user database - in production, use a real database
const users: User[] = [
  {
    id: '1',
    username: 'admin',
    email: '<EMAIL>',
    role: 'admin',
    createdAt: new Date(),
  },
  {
    id: '2',
    username: 'analyst',
    email: '<EMAIL>',
    role: 'analyst',
    createdAt: new Date(),
  },
  {
    id: '3',
    username: 'viewer',
    email: '<EMAIL>',
    role: 'viewer',
    createdAt: new Date(),
  }
];

// Mock password storage - in production, store hashed passwords in database
const passwords: Record<string, string> = {
  'admin': await bcrypt.hash('admin123', 10),
  'analyst': await bcrypt.hash('analyst123', 10),
  'viewer': await bcrypt.hash('viewer123', 10),
};

/**
 * POST /api/auth/login
 * Authenticate user and return JWT token
 */
router.post(
  '/login',
  authRateLimit,
  validateBody(schemas.auth),
  asyncHandler(async (req, res) => {
    const { username, password } = req.body;

    // Find user
    const user = users.find(u => u.username === username);
    if (!user) {
      throw createError('Invalid credentials', 401);
    }

    // Verify password
    const isValidPassword = await bcrypt.compare(password, passwords[username]);
    if (!isValidPassword) {
      throw createError('Invalid credentials', 401);
    }

    // Generate token
    const token = generateToken({
      userId: user.id,
      username: user.username,
      role: user.role,
    });

    // Update last login
    user.lastLogin = new Date();

    const response: ApiResponse = {
      success: true,
      data: {
        token,
        user: {
          id: user.id,
          username: user.username,
          email: user.email,
          role: user.role,
        },
      },
      message: 'Login successful',
      timestamp: new Date().toISOString()
    };

    res.json(response);
  })
);

/**
 * GET /api/auth/me
 * Get current user information
 */
router.get(
  '/me',
  authenticateToken,
  asyncHandler(async (req, res) => {
    const user = users.find(u => u.id === req.user?.userId);
    if (!user) {
      throw createError('User not found', 404);
    }

    const response: ApiResponse = {
      success: true,
      data: {
        id: user.id,
        username: user.username,
        email: user.email,
        role: user.role,
        lastLogin: user.lastLogin,
      },
      timestamp: new Date().toISOString()
    };

    res.json(response);
  })
);

/**
 * POST /api/auth/logout
 * Logout user (client-side token removal)
 */
router.post(
  '/logout',
  authenticateToken,
  asyncHandler(async (req, res) => {
    const response: ApiResponse = {
      success: true,
      message: 'Logout successful',
      timestamp: new Date().toISOString()
    };

    res.json(response);
  })
);

export default router;
