import { Router } from 'express';
import { QueryController } from '../controllers/queryController.js';
import { authenticateToken, requireRole } from '../middleware/auth.js';
import { queryRateLimit } from '../middleware/rateLimiter.js';
import { validateBody } from '../middleware/validation.js';
import { schemas } from '../middleware/validation.js';
import { asyncHandler } from '../middleware/errorHandler.js';

const router = Router();
const queryController = new QueryController();

// Initialize the controller asynchronously
queryController.initialize().catch(console.error);

// Apply authentication and rate limiting to all routes
router.use(authenticateToken);
router.use(queryRateLimit);

/**
 * POST /api/query
 * Natural language log queries
 */
router.post(
  '/',
  requireRole(['admin', 'analyst', 'viewer']),
  validate<PERSON><PERSON>(schemas.logQuery),
  async<PERSON>and<PERSON>(queryController.queryLogs)
);

/**
 * POST /api/query/similar
 * Find similar log entries
 */
router.post(
  '/similar',
  requireRole(['admin', 'analyst', 'viewer']),
  asyncHandler(queryController.findSimilarLogs)
);

/**
 * POST /api/query/translate
 * Translate natural language to structured filters
 */
router.post(
  '/translate',
  requireRole(['admin', 'analyst', 'viewer']),
  asyncHandler(queryController.translateQuery)
);

/**
 * GET /api/query/stats
 * Get query service statistics
 */
router.get(
  '/stats',
  requireRole(['admin', 'analyst']),
  asyncHandler(queryController.getStats)
);

export default router;
