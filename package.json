{"name": "log-analyzer-rag", "version": "1.0.0", "description": "RAG-powered log analysis application with Next.js frontend and Node.js backend", "private": true, "workspaces": ["apps/*", "libs/*"], "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev1": "concurrently \"npm run dev:backend\"", "dev:frontend": "cd apps/frontend && npm run dev", "dev:backend": "cd apps/backend && npm run dev", "build": "npm run build:backend && npm run build:frontend", "build:frontend": "cd apps/frontend && npm run build", "build:backend": "cd apps/backend && npm run build", "test": "npm run test:backend && npm run test:frontend", "test:frontend": "cd apps/frontend && npm run test", "test:backend": "cd apps/backend && npm run test", "lint": "npm run lint:frontend && npm run lint:backend", "lint:frontend": "cd apps/frontend && npm run lint", "lint:backend": "cd apps/backend && npm run lint", "clean": "rm -rf node_modules apps/*/node_modules libs/*/node_modules apps/*/.next apps/*/dist"}, "devDependencies": {"@types/node": "^20.10.0", "concurrently": "^8.2.2", "typescript": "^5.3.0"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}