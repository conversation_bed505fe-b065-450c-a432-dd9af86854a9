import { createReadStream } from 'fs';
import { createInterface } from 'readline';
import { stat } from 'fs/promises';
import { LogParser } from './logParser.js';
import { BatchParseResult, ParsedLogEntry, LogSource } from './types.js';

export interface FileProcessorOptions {
  batchSize?: number;
  maxFileSize?: number; // in bytes
  encoding?: BufferEncoding;
  onProgress?: (processed: number, total: number) => void;
  onBatch?: (batch: ParsedLogEntry[]) => Promise<void>;
}

export class FileProcessor {
  private parser: LogParser;
  private options: Required<FileProcessorOptions>;

  constructor(parser: LogParser, options: FileProcessorOptions = {}) {
    this.parser = parser;
    this.options = {
      batchSize: 5000,
      maxFileSize: 10 * 1024 * 1024 * 1024, // 10GB
      encoding: 'utf8',
      onProgress: () => {},
      onBatch: async () => {},
      ...options
    };
  }

  /**
   * Process a single log file
   */
  async processFile(filePath: string): Promise<BatchParseResult> {
    try {
      // Check file size
      const stats = await stat(filePath);
      if (stats.size > this.options.maxFileSize) {
        throw new Error(`File size ${stats.size} exceeds maximum allowed size ${this.options.maxFileSize}`);
      }

      const result: BatchParseResult = {
        totalLines: 0,
        successfullyParsed: 0,
        failed: 0,
        entries: [],
        errors: []
      };

      const fileStream = createReadStream(filePath, { encoding: this.options.encoding });
      const rl = createInterface({
        input: fileStream,
        crlfDelay: Infinity
      });

      let currentBatch: string[] = [];
      let lineNumber = 0;

      for await (const line of rl) {
        lineNumber++;
        result.totalLines++;
        
        currentBatch.push(line);

        // Process batch when it reaches the specified size
        if (currentBatch.length >= this.options.batchSize) {
          await this.processBatch(currentBatch, filePath, lineNumber - currentBatch.length + 1, result);
          currentBatch = [];
        }

        // Report progress
        if (lineNumber % 1000 === 0) {
          this.options.onProgress(lineNumber, stats.size);
        }
      }

      // Process remaining lines
      if (currentBatch.length > 0) {
        await this.processBatch(currentBatch, filePath, lineNumber - currentBatch.length + 1, result);
      }

      return result;

    } catch (error) {
      throw new Error(`Failed to process file ${filePath}: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Process multiple log files from specified sources
   */
  async processLogSources(sources: LogSource[]): Promise<Map<string, BatchParseResult>> {
    const results = new Map<string, BatchParseResult>();

    for (const source of sources) {
      try {
        console.log(`Processing log source: ${source.path}`);
        const result = await this.processDirectory(source.path, source.module);
        results.set(source.path, result);
      } catch (error) {
        console.error(`Failed to process source ${source.path}:`, error);
        // Continue with other sources
        results.set(source.path, {
          totalLines: 0,
          successfullyParsed: 0,
          failed: 0,
          entries: [],
          errors: [{
            lineNumber: 0,
            line: '',
            error: error instanceof Error ? error.message : 'Unknown error'
          }]
        });
      }
    }

    return results;
  }

  /**
   * Process all log files in a directory
   */
  async processDirectory(directoryPath: string, module?: string): Promise<BatchParseResult> {
    const { readdir } = await import('fs/promises');
    const { join } = await import('path');
    
    try {
      const files = await readdir(directoryPath);
      const logFiles = files.filter(file => 
        file.endsWith('.log') || 
        file.endsWith('.txt') || 
        !file.includes('.')
      );

      const combinedResult: BatchParseResult = {
        totalLines: 0,
        successfullyParsed: 0,
        failed: 0,
        entries: [],
        errors: []
      };

      for (const file of logFiles) {
        const filePath = join(directoryPath, file);
        try {
          const fileResult = await this.processFile(filePath);
          
          // Merge results
          combinedResult.totalLines += fileResult.totalLines;
          combinedResult.successfullyParsed += fileResult.successfullyParsed;
          combinedResult.failed += fileResult.failed;
          combinedResult.entries.push(...fileResult.entries);
          combinedResult.errors.push(...fileResult.errors);

        } catch (error) {
          console.error(`Failed to process file ${filePath}:`, error);
          combinedResult.errors.push({
            lineNumber: 0,
            line: filePath,
            error: error instanceof Error ? error.message : 'Unknown error'
          });
        }
      }

      return combinedResult;

    } catch (error) {
      throw new Error(`Failed to read directory ${directoryPath}: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private async processBatch(
    lines: string[], 
    sourceFile: string, 
    startLineNumber: number, 
    result: BatchParseResult
  ): Promise<void> {
    const batchResult = this.parser.parseLines(lines, sourceFile);
    
    // Update counters
    result.successfullyParsed += batchResult.successfullyParsed;
    result.failed += batchResult.failed;
    
    // Adjust line numbers
    batchResult.errors.forEach(error => {
      error.lineNumber += startLineNumber - 1;
    });
    
    result.errors.push(...batchResult.errors);
    result.entries.push(...batchResult.entries);

    // Call batch callback if provided
    if (batchResult.entries.length > 0) {
      await this.options.onBatch(batchResult.entries);
    }
  }

  /**
   * Get processing statistics
   */
  getProcessingStats(result: BatchParseResult): {
    successRate: number;
    errorRate: number;
    avgResponseTime?: number;
    statusCodeDistribution: Map<number, number>;
    moduleDistribution: Map<string, number>;
  } {
    const successRate = result.totalLines > 0 ? (result.successfullyParsed / result.totalLines) * 100 : 0;
    const errorRate = result.totalLines > 0 ? (result.failed / result.totalLines) * 100 : 0;
    
    const responseTimes = result.entries.map(entry => entry.responseTime).filter(rt => rt > 0);
    const avgResponseTime = responseTimes.length > 0 
      ? responseTimes.reduce((sum, rt) => sum + rt, 0) / responseTimes.length 
      : undefined;

    const statusCodeDistribution = new Map<number, number>();
    const moduleDistribution = new Map<string, number>();

    result.entries.forEach(entry => {
      // Status code distribution
      const currentStatusCount = statusCodeDistribution.get(entry.status) || 0;
      statusCodeDistribution.set(entry.status, currentStatusCount + 1);

      // Module distribution
      const currentModuleCount = moduleDistribution.get(entry.module) || 0;
      moduleDistribution.set(entry.module, currentModuleCount + 1);
    });

    return {
      successRate,
      errorRate,
      avgResponseTime,
      statusCodeDistribution,
      moduleDistribution
    };
  }
}
