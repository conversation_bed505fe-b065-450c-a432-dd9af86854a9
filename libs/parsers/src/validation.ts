import Joi from 'joi';
import { LogLevel, HttpMethod } from './types.js';

export const logEntrySchema = Joi.object({
  timestamp: Joi.date().required(),
  level: Joi.string().valid(...Object.values(LogLevel)).required(),
  module: Joi.string().min(1).max(50).required(),
  sessionId: Joi.string().min(1).max(100).required(),
  platform: Joi.string().min(1).max(50).required(),
  customerId: Joi.string().min(1).max(100).required(),
  clientIp: Joi.string().ip({ version: ['ipv4', 'ipv6'] }).required(),
  requestId: Joi.string().min(1).max(100).required(),
  auditType: Joi.string().min(1).max(50).required(),
  appName: Joi.string().min(1).max(50).required(),
  responseTime: Joi.number().min(0).max(300000).required(), // Max 5 minutes
  status: Joi.number().min(100).max(599).required(), // HTTP status codes
  method: Joi.string().valid(...Object.values(HttpMethod)).required(),
  originUrl: Joi.string().uri().required(),
  extraData: Joi.string().allow('').max(1000),
  rawData: Joi.string().allow('').max(5000),
  sourceFile: Joi.string().optional(),
  lineNumber: Joi.number().optional(),
  parsedAt: Joi.date().required()
});

export const configSchema = Joi.object({
  strictMode: Joi.boolean().default(false),
  skipMalformed: Joi.boolean().default(true),
  maxLineLength: Joi.number().min(100).max(10000).default(2000),
  dateFormat: Joi.string().optional(),
  timezone: Joi.string().optional()
});

export function validateLogEntry(entry: any): { error?: string; value?: any } {
  const { error, value } = logEntrySchema.validate(entry, { 
    abortEarly: false,
    stripUnknown: true 
  });
  
  if (error) {
    return { 
      error: error.details.map(detail => detail.message).join('; ') 
    };
  }
  
  return { value };
}

export function validateConfig(config: any): { error?: string; value?: any } {
  const { error, value } = configSchema.validate(config, { 
    abortEarly: false,
    stripUnknown: true 
  });
  
  if (error) {
    return { 
      error: error.details.map(detail => detail.message).join('; ') 
    };
  }
  
  return { value };
}
