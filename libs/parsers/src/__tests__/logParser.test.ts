import { LogParser } from '../logParser.js';
import { Log<PERSON><PERSON>l, HttpMethod } from '../types.js';

describe('LogParser', () => {
  let parser: LogParser;

  beforeEach(() => {
    parser = new LogParser();
  });

  describe('parseLine', () => {
    it('should parse a valid log line correctly', () => {
      const logLine = '[2024-01-15 10:30:45.123][INFO][flights][sess123][web][cust456][*************][req789][booking][FlightApp][1250][200][GET][https://example.com/api/flights][extra data] : User searched for flights from NYC to LAX';

      const result = parser.parseLine(logLine, 1, 'test.log');

      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      
      if (result.data) {
        expect(result.data.level).toBe(LogLevel.INFO);
        expect(result.data.module).toBe('flights');
        expect(result.data.sessionId).toBe('sess123');
        expect(result.data.platform).toBe('web');
        expect(result.data.customerId).toBe('cust456');
        expect(result.data.clientIp).toBe('*************');
        expect(result.data.requestId).toBe('req789');
        expect(result.data.auditType).toBe('booking');
        expect(result.data.appName).toBe('FlightApp');
        expect(result.data.responseTime).toBe(1250);
        expect(result.data.status).toBe(200);
        expect(result.data.method).toBe(HttpMethod.GET);
        expect(result.data.originUrl).toBe('https://example.com/api/flights');
        expect(result.data.extraData).toBe('extra data');
        expect(result.data.rawData).toBe('User searched for flights from NYC to LAX');
        expect(result.data.sourceFile).toBe('test.log');
        expect(result.data.lineNumber).toBe(1);
      }
    });

    it('should handle malformed log lines', () => {
      const malformedLine = 'This is not a valid log line';

      const result = parser.parseLine(malformedLine, 1);

      expect(result.success).toBe(false);
      expect(result.error).toContain('does not match expected log format');
    });

    it('should handle empty lines', () => {
      const result = parser.parseLine('', 1);

      expect(result.success).toBe(false);
      expect(result.error).toBe('Empty line');
    });

    it('should handle invalid timestamps', () => {
      const logLine = '[invalid-timestamp][INFO][flights][sess123][web][cust456][*************][req789][booking][FlightApp][1250][200][GET][https://example.com/api/flights][extra data] : User searched for flights';

      const result = parser.parseLine(logLine, 1);

      expect(result.success).toBe(false);
      expect(result.error).toContain('Invalid timestamp format');
    });

    it('should handle invalid log levels', () => {
      const logLine = '[2024-01-15 10:30:45.123][INVALID][flights][sess123][web][cust456][*************][req789][booking][FlightApp][1250][200][GET][https://example.com/api/flights][extra data] : User searched for flights';

      const result = parser.parseLine(logLine, 1);

      expect(result.success).toBe(false);
      expect(result.error).toContain('Invalid log level');
    });

    it('should handle invalid response times', () => {
      const logLine = '[2024-01-15 10:30:45.123][INFO][flights][sess123][web][cust456][*************][req789][booking][FlightApp][invalid][200][GET][https://example.com/api/flights][extra data] : User searched for flights';

      const result = parser.parseLine(logLine, 1);

      expect(result.success).toBe(false);
      expect(result.error).toContain('Invalid response time');
    });

    it('should handle invalid status codes', () => {
      const logLine = '[2024-01-15 10:30:45.123][INFO][flights][sess123][web][cust456][*************][req789][booking][FlightApp][1250][999][GET][https://example.com/api/flights][extra data] : User searched for flights';

      const result = parser.parseLine(logLine, 1);

      expect(result.success).toBe(false);
      expect(result.error).toContain('Invalid status code');
    });

    it('should handle invalid HTTP methods', () => {
      const logLine = '[2024-01-15 10:30:45.123][INFO][flights][sess123][web][cust456][*************][req789][booking][FlightApp][1250][200][INVALID][https://example.com/api/flights][extra data] : User searched for flights';

      const result = parser.parseLine(logLine, 1);

      expect(result.success).toBe(false);
      expect(result.error).toContain('Invalid HTTP method');
    });
  });

  describe('parseLines', () => {
    it('should parse multiple valid log lines', () => {
      const logLines = [
        '[2024-01-15 10:30:45.123][INFO][flights][sess123][web][cust456][*************][req789][booking][FlightApp][1250][200][GET][https://example.com/api/flights][extra data] : User searched for flights',
        '[2024-01-15 10:31:00.456][ERROR][hotels][sess124][mobile][cust457][*************][req790][search][HotelApp][2500][500][POST][https://example.com/api/hotels][] : Database connection failed'
      ];

      const result = parser.parseLines(logLines, 'test.log');

      expect(result.totalLines).toBe(2);
      expect(result.successfullyParsed).toBe(2);
      expect(result.failed).toBe(0);
      expect(result.entries).toHaveLength(2);
      expect(result.errors).toHaveLength(0);
    });

    it('should handle mixed valid and invalid lines', () => {
      const logLines = [
        '[2024-01-15 10:30:45.123][INFO][flights][sess123][web][cust456][*************][req789][booking][FlightApp][1250][200][GET][https://example.com/api/flights][extra data] : User searched for flights',
        'This is an invalid log line',
        '[2024-01-15 10:31:00.456][ERROR][hotels][sess124][mobile][cust457][*************][req790][search][HotelApp][2500][500][POST][https://example.com/api/hotels][] : Database connection failed'
      ];

      const result = parser.parseLines(logLines, 'test.log');

      expect(result.totalLines).toBe(3);
      expect(result.successfullyParsed).toBe(2);
      expect(result.failed).toBe(1);
      expect(result.entries).toHaveLength(2);
      expect(result.errors).toHaveLength(1);
    });
  });

  describe('configuration', () => {
    it('should use custom configuration', () => {
      const customParser = new LogParser({
        strictMode: true,
        skipMalformed: false,
        maxLineLength: 1000
      });

      const config = customParser.getConfig();
      expect(config.strictMode).toBe(true);
      expect(config.skipMalformed).toBe(false);
      expect(config.maxLineLength).toBe(1000);
    });

    it('should throw error in strict mode for invalid lines', () => {
      const strictParser = new LogParser({ strictMode: true });
      const logLines = [
        '[2024-01-15 10:30:45.123][INFO][flights][sess123][web][cust456][*************][req789][booking][FlightApp][1250][200][GET][https://example.com/api/flights][extra data] : User searched for flights',
        'This is an invalid log line'
      ];

      expect(() => {
        strictParser.parseLines(logLines);
      }).toThrow();
    });
  });
});
