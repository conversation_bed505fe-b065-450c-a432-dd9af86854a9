export interface ParsedLogEntry {
  timestamp: Date;
  level: LogLevel;
  module: string;
  sessionId: string;
  platform: string;
  customerId: string;
  clientIp: string;
  requestId: string;
  auditType: string;
  appName: string;
  responseTime: number;
  status: number;
  method: HttpMethod;
  originUrl: string;
  extraData: string;
  rawData: string;
  // Metadata for processing
  sourceFile?: string;
  lineNumber?: number;
  parsedAt: Date;
}

export enum LogLevel {
  DEBUG = 'DEBUG',
  INFO = 'INFO',
  WARN = 'WARN',
  ERROR = 'ERROR',
  FATAL = 'FATAL'
}

export enum HttpMethod {
  GET = 'GET',
  POST = 'POST',
  PUT = 'PUT',
  DELETE = 'DELETE',
  PATCH = 'PATCH',
  HEAD = 'HEAD',
  OPTIONS = 'OPTIONS'
}

export interface ParseResult {
  success: boolean;
  data?: ParsedLogEntry;
  error?: string;
  lineNumber?: number;
}

export interface BatchParseResult {
  totalLines: number;
  successfullyParsed: number;
  failed: number;
  entries: ParsedLogEntry[];
  errors: Array<{
    lineNumber: number;
    line: string;
    error: string;
  }>;
}

export interface LogParserConfig {
  strictMode: boolean;
  skipMalformed: boolean;
  maxLineLength: number;
  dateFormat?: string;
  timezone?: string;
}

export interface LogSource {
  path: string;
  module: string;
  lastProcessed?: Date;
  totalLines?: number;
  processedLines?: number;
}

export const LOG_SOURCES: LogSource[] = [
  { path: '/log/axisLogs/flights', module: 'flights' },
  { path: '/log/axisLogs/hotels', module: 'hotels' },
  { path: '/log/axisLogs/activities', module: 'activities' },
  { path: '/log/axisLogs/pointTransfer', module: 'pointTransfer' },
  { path: '/log/axisLogs/loyalty', module: 'loyalty' },
  { path: '/log/axisLogs/common', module: 'common' }
];
