import moment from 'moment';
import { 
  <PERSON>rsedLog<PERSON>ntry, 
  ParseResult, 
  BatchParseResult, 
  LogParserConfig, 
  LogLevel, 
  HttpMethod 
} from './types.js';
import { validateLogEntry, validateConfig } from './validation.js';

export class LogParser {
  private config: LogParserConfig;
  
  // Log format: [timestamp][level][module][sessionId][platform][customerId][clientIp][requestId][auditType][appName][responseTime][status][method][originUrl][extraData] : rawData
  private readonly LOG_PATTERN = /^\[([^\]]+)\]\[([^\]]+)\]\[([^\]]+)\]\[([^\]]+)\]\[([^\]]+)\]\[([^\]]+)\]\[([^\]]+)\]\[([^\]]+)\]\[([^\]]+)\]\[([^\]]+)\]\[([^\]]+)\]\[([^\]]+)\]\[([^\]]+)\]\[([^\]]+)\]\[([^\]]*)\]\s*:\s*(.*)$/;

  constructor(config: Partial<LogParserConfig> = {}) {
    const { error, value } = validateConfig(config);
    if (error) {
      throw new Error(`Invalid configuration: ${error}`);
    }
    
    this.config = {
      strictMode: false,
      skipMalformed: true,
      maxLineLength: 2000,
      dateFormat: 'YYYY-MM-DD HH:mm:ss.SSS',
      timezone: 'UTC',
      ...value
    };
  }

  /**
   * Parse a single log line
   */
  public parseLine(line: string, lineNumber?: number, sourceFile?: string): ParseResult {
    try {
      // Basic validation
      if (!line || line.trim().length === 0) {
        return {
          success: false,
          error: 'Empty line',
          lineNumber
        };
      }

      if (line.length > this.config.maxLineLength) {
        return {
          success: false,
          error: `Line exceeds maximum length of ${this.config.maxLineLength} characters`,
          lineNumber
        };
      }

      // Extract fields using regex
      const match = line.match(this.LOG_PATTERN);
      if (!match) {
        return {
          success: false,
          error: 'Line does not match expected log format',
          lineNumber
        };
      }

      const [
        , // full match
        timestampStr,
        levelStr,
        module,
        sessionId,
        platform,
        customerId,
        clientIp,
        requestId,
        auditType,
        appName,
        responseTimeStr,
        statusStr,
        method,
        originUrl,
        extraData,
        rawData
      ] = match;

      // Parse and validate individual fields
      const timestamp = this.parseTimestamp(timestampStr);
      if (!timestamp) {
        return {
          success: false,
          error: `Invalid timestamp format: ${timestampStr}`,
          lineNumber
        };
      }

      const level = this.parseLogLevel(levelStr);
      if (!level) {
        return {
          success: false,
          error: `Invalid log level: ${levelStr}`,
          lineNumber
        };
      }

      const responseTime = this.parseNumber(responseTimeStr, 'responseTime');
      if (responseTime === null) {
        return {
          success: false,
          error: `Invalid response time: ${responseTimeStr}`,
          lineNumber
        };
      }

      const status = this.parseNumber(statusStr, 'status');
      if (status === null) {
        return {
          success: false,
          error: `Invalid status code: ${statusStr}`,
          lineNumber
        };
      }

      const httpMethod = this.parseHttpMethod(method);
      if (!httpMethod) {
        return {
          success: false,
          error: `Invalid HTTP method: ${method}`,
          lineNumber
        };
      }

      // Create parsed entry
      const entry: ParsedLogEntry = {
        timestamp,
        level,
        module: module.trim(),
        sessionId: sessionId.trim(),
        platform: platform.trim(),
        customerId: customerId.trim(),
        clientIp: clientIp.trim(),
        requestId: requestId.trim(),
        auditType: auditType.trim(),
        appName: appName.trim(),
        responseTime,
        status,
        method: httpMethod,
        originUrl: originUrl.trim(),
        extraData: extraData.trim(),
        rawData: rawData.trim(),
        sourceFile,
        lineNumber,
        parsedAt: new Date()
      };

      // Validate the complete entry
      const validation = validateLogEntry(entry);
      if (validation.error) {
        return {
          success: false,
          error: `Validation failed: ${validation.error}`,
          lineNumber
        };
      }

      return {
        success: true,
        data: validation.value
      };

    } catch (error) {
      return {
        success: false,
        error: `Parsing error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        lineNumber
      };
    }
  }

  /**
   * Parse multiple log lines
   */
  public parseLines(lines: string[], sourceFile?: string): BatchParseResult {
    const result: BatchParseResult = {
      totalLines: lines.length,
      successfullyParsed: 0,
      failed: 0,
      entries: [],
      errors: []
    };

    lines.forEach((line, index) => {
      const lineNumber = index + 1;
      const parseResult = this.parseLine(line, lineNumber, sourceFile);
      
      if (parseResult.success && parseResult.data) {
        result.entries.push(parseResult.data);
        result.successfullyParsed++;
      } else {
        result.failed++;
        if (!this.config.skipMalformed || !this.config.strictMode) {
          result.errors.push({
            lineNumber,
            line: line.substring(0, 200) + (line.length > 200 ? '...' : ''),
            error: parseResult.error || 'Unknown error'
          });
        }
        
        if (this.config.strictMode) {
          throw new Error(`Strict mode: Failed to parse line ${lineNumber}: ${parseResult.error}`);
        }
      }
    });

    return result;
  }

  private parseTimestamp(timestampStr: string): Date | null {
    try {
      const parsed = moment(timestampStr, this.config.dateFormat, true);
      if (!parsed.isValid()) {
        // Try common formats as fallback
        const fallbackFormats = [
          'YYYY-MM-DD HH:mm:ss',
          'YYYY-MM-DDTHH:mm:ss.SSSZ',
          'YYYY-MM-DDTHH:mm:ssZ',
          'MM/DD/YYYY HH:mm:ss'
        ];
        
        for (const format of fallbackFormats) {
          const fallbackParsed = moment(timestampStr, format, true);
          if (fallbackParsed.isValid()) {
            return fallbackParsed.toDate();
          }
        }
        return null;
      }
      return parsed.toDate();
    } catch {
      return null;
    }
  }

  private parseLogLevel(levelStr: string): LogLevel | null {
    const level = levelStr.toUpperCase() as LogLevel;
    return Object.values(LogLevel).includes(level) ? level : null;
  }

  private parseHttpMethod(methodStr: string): HttpMethod | null {
    const method = methodStr.toUpperCase() as HttpMethod;
    return Object.values(HttpMethod).includes(method) ? method : null;
  }

  private parseNumber(numStr: string, fieldName: string): number | null {
    const num = parseFloat(numStr);
    if (isNaN(num)) {
      return null;
    }
    
    // Additional validation based on field
    if (fieldName === 'status' && (num < 100 || num > 599)) {
      return null;
    }
    
    if (fieldName === 'responseTime' && (num < 0 || num > 300000)) {
      return null;
    }
    
    return num;
  }

  /**
   * Get parser configuration
   */
  public getConfig(): LogParserConfig {
    return { ...this.config };
  }

  /**
   * Update parser configuration
   */
  public updateConfig(newConfig: Partial<LogParserConfig>): void {
    const { error, value } = validateConfig({ ...this.config, ...newConfig });
    if (error) {
      throw new Error(`Invalid configuration: ${error}`);
    }
    this.config = value;
  }
}
