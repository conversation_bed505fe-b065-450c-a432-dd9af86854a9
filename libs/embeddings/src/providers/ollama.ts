import axios from 'axios';
import { EmbeddingProvider } from '../types.js';

export interface OllamaEmbeddingConfig {
  provider: 'ollama';
  baseUrl?: string;
  model: string;
}

export class OllamaEmbeddingProvider implements EmbeddingProvider {
  private baseUrl: string;
  private model: string;
  private isInitialized = false;
  private dimension = 768; // Default dimension for nomic-embed-text
  private maxTokens = 8192; // Default max tokens

  constructor(private config: OllamaEmbeddingConfig) {
    this.baseUrl = config.baseUrl || 'http://localhost:11434';
    this.model = config.model || 'nomic-embed-text';
  }

  async initialize(): Promise<void> {
    try {
      // Check if Ollama is available
      await axios.get(`${this.baseUrl}/api/version`);

      // Check if the model is available, if not try to pull it
      const modelsResponse = await axios.get(`${this.baseUrl}/api/tags`);
      const availableModels = modelsResponse.data.models?.map((m: any) => m.name) || [];

      if (!availableModels.some((name: string) => name.includes(this.model))) {
        console.log(`Model ${this.model} not found. Attempting to pull...`);
        try {
          await axios.post(`${this.baseUrl}/api/pull`, {
            name: this.model
          });
          console.log(`Model ${this.model} pulled successfully`);
        } catch (pullError) {
          console.warn(`Failed to pull model ${this.model}:`, pullError);
          throw new Error(`Model ${this.model} not available and failed to pull. Please run: ollama pull ${this.model}`);
        }
      }

      // Detect the actual dimension by generating a test embedding
      await this.detectDimension();

      this.isInitialized = true;
      console.log(`Ollama embedding provider initialized with model: ${this.model}, dimension: ${this.dimension}`);
    } catch (error) {
      throw new Error(`Failed to initialize Ollama embedding provider: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private async detectDimension(): Promise<void> {
    try {
      const testEmbedding = await this.generateEmbedding('test');
      this.dimension = testEmbedding.length;
      console.log(`Detected embedding dimension: ${this.dimension} for model: ${this.model}`);
    } catch (error) {
      console.warn(`Failed to detect dimension, using default: ${this.dimension}`);
    }
  }

  async generateEmbedding(text: string): Promise<number[]> {
    if (!this.isInitialized) {
      throw new Error('Ollama embedding provider not initialized');
    }

    try {
      const response = await axios.post(`${this.baseUrl}/api/embeddings`, {
        model: this.model,
        prompt: text,
      });

      if (!response.data.embedding || !Array.isArray(response.data.embedding)) {
        throw new Error('Invalid response from Ollama API');
      }

      return response.data.embedding;
    } catch (error) {
      throw new Error(`Failed to generate embedding: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async generateBatchEmbeddings(texts: string[]): Promise<number[][]> {
    if (!this.isInitialized) {
      throw new Error('Ollama embedding provider not initialized');
    }

    try {
      // Process in batches to avoid overwhelming the API
      const batchSize = 10;
      const embeddings: number[][] = [];

      for (let i = 0; i < texts.length; i += batchSize) {
        const batch = texts.slice(i, i + batchSize);
        const batchPromises = batch.map(text => this.generateEmbedding(text));
        const batchResults = await Promise.all(batchPromises);
        embeddings.push(...batchResults);
      }

      return embeddings;
    } catch (error) {
      throw new Error(`Failed to generate embeddings: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  getDimension(): number {
    return this.dimension;
  }

  getMaxTokens(): number {
    return this.maxTokens;
  }

  async close(): Promise<void> {
    // No specific cleanup needed for Ollama
    this.isInitialized = false;
  }
}
