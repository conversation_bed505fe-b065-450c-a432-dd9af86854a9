import { v4 as uuidv4 } from 'uuid';
import { 
  EmbeddingProvider, 
  VectorDatabase, 
  EmbeddingVector, 
  SearchQuery, 
  SearchResult, 
  BatchEmbeddingResult,
  LogMetadata 
} from './types.js';
import { OpenAIEmbeddingProvider } from './providers/openai.js';

export class EmbeddingService {
  private embeddingProvider: EmbeddingProvider;
  private vectorDatabase: VectorDatabase;
  private isInitialized = false;

  constructor(embeddingProvider: EmbeddingProvider, vectorDatabase: VectorDatabase) {
    this.embeddingProvider = embeddingProvider;
    this.vectorDatabase = vectorDatabase;
  }

  async initialize(): Promise<void> {
    try {
      // Initialize embedding provider first
      if ('initialize' in this.embeddingProvider && typeof this.embeddingProvider.initialize === 'function') {
        await this.embeddingProvider.initialize();
      }

      // Then initialize vector database
      await this.vectorDatabase.initialize();

      this.isInitialized = true;
      console.log('Embedding service initialized successfully');
    } catch (error) {
      throw new Error(`Failed to initialize embedding service: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Process and store log entries as embeddings
   */
  async processLogEntries(logEntries: any[]): Promise<BatchEmbeddingResult> {
    if (!this.isInitialized) {
      throw new Error('Embedding service not initialized');
    }

    const result: BatchEmbeddingResult = {
      successful: 0,
      failed: 0,
      embeddings: [],
      errors: []
    };

    try {
      // Prepare texts for embedding
      const texts = logEntries.map(entry => OpenAIEmbeddingProvider.prepareLogText(entry));
      
      // Generate embeddings in batches
      const embeddings = await this.embeddingProvider.generateBatchEmbeddings(texts);

      // Create embedding vectors
      const embeddingVectors: EmbeddingVector[] = [];
      
      for (let i = 0; i < logEntries.length; i++) {
        try {
          const logEntry = logEntries[i];
          const embedding = embeddings[i];
          
          if (!embedding || embedding.length === 0) {
            result.failed++;
            result.errors.push({
              id: logEntry.requestId || `unknown-${i}`,
              error: 'Failed to generate embedding'
            });
            continue;
          }

          const embeddingVector: EmbeddingVector = {
            id: uuidv4(),
            vector: embedding,
            metadata: this.extractMetadata(logEntry),
            text: texts[i],
            createdAt: new Date()
          };

          embeddingVectors.push(embeddingVector);
          result.successful++;
        } catch (error) {
          result.failed++;
          result.errors.push({
            id: logEntries[i].requestId || `unknown-${i}`,
            error: error instanceof Error ? error.message : 'Unknown error'
          });
        }
      }

      // Store in vector database
      if (embeddingVectors.length > 0) {
        await this.vectorDatabase.insert(embeddingVectors);
        result.embeddings = embeddingVectors;
      }

      console.log(`Processed ${result.successful} log entries, ${result.failed} failed`);
      return result;

    } catch (error) {
      throw new Error(`Failed to process log entries: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Search for similar log entries using natural language query
   */
  async searchLogs(queryText: string, options: Partial<SearchQuery> = {}): Promise<SearchResult[]> {
    if (!this.isInitialized) {
      throw new Error('Embedding service not initialized');
    }

    try {
      // Prepare query text
      const preparedQuery = OpenAIEmbeddingProvider.prepareQueryText(queryText);
      
      // Generate embedding for the query
      const queryEmbedding = await this.embeddingProvider.generateEmbedding(preparedQuery);

      // Search in vector database
      const searchQuery: SearchQuery = {
        text: preparedQuery,
        vector: queryEmbedding, // Add vector to query for database search
        limit: options.limit || 10,
        threshold: options.threshold || 0.7,
        filters: options.filters,
        includeMetadata: options.includeMetadata || true
      } as SearchQuery & { vector: number[] };

      const results = await this.vectorDatabase.search(searchQuery);
      
      console.log(`Found ${results.length} similar log entries for query: "${queryText}"`);
      return results;

    } catch (error) {
      throw new Error(`Failed to search logs: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Find similar log entries to a given log entry
   */
  async findSimilarLogs(logEntry: any, options: Partial<SearchQuery> = {}): Promise<SearchResult[]> {
    if (!this.isInitialized) {
      throw new Error('Embedding service not initialized');
    }

    try {
      // Prepare log text
      const logText = OpenAIEmbeddingProvider.prepareLogText(logEntry);
      
      // Generate embedding
      const embedding = await this.embeddingProvider.generateEmbedding(logText);

      // Search for similar entries
      const searchQuery: SearchQuery = {
        text: logText,
        vector: embedding,
        limit: options.limit || 10,
        threshold: options.threshold || 0.8, // Higher threshold for similarity
        filters: options.filters,
        includeMetadata: options.includeMetadata || true
      } as SearchQuery & { vector: number[] };

      const results = await this.vectorDatabase.search(searchQuery);
      
      // Filter out the exact same entry if it exists
      const filteredResults = results.filter(result => 
        result.metadata.requestId !== logEntry.requestId ||
        result.metadata.timestamp !== logEntry.timestamp.toISOString()
      );

      console.log(`Found ${filteredResults.length} similar log entries`);
      return filteredResults;

    } catch (error) {
      throw new Error(`Failed to find similar logs: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get database statistics
   */
  async getStats() {
    if (!this.isInitialized) {
      throw new Error('Embedding service not initialized');
    }

    return await this.vectorDatabase.getStats();
  }

  /**
   * Close the service and cleanup resources
   */
  async close(): Promise<void> {
    if (this.isInitialized) {
      await this.vectorDatabase.close();
      this.isInitialized = false;
      console.log('Embedding service closed');
    }
  }

  private extractMetadata(logEntry: any): LogMetadata {
    return {
      timestamp: logEntry.timestamp.toISOString(),
      level: logEntry.level,
      module: logEntry.module,
      sessionId: logEntry.sessionId,
      platform: logEntry.platform,
      customerId: logEntry.customerId,
      clientIp: logEntry.clientIp,
      requestId: logEntry.requestId,
      auditType: logEntry.auditType,
      appName: logEntry.appName,
      responseTime: logEntry.responseTime,
      status: logEntry.status,
      method: logEntry.method,
      originUrl: logEntry.originUrl,
      sourceFile: logEntry.sourceFile,
      lineNumber: logEntry.lineNumber
    };
  }

  /**
   * Delete embeddings by IDs
   */
  async deleteEmbeddings(ids: string[]): Promise<void> {
    if (!this.isInitialized) {
      throw new Error('Embedding service not initialized');
    }

    await this.vectorDatabase.delete(ids);
    console.log(`Deleted ${ids.length} embeddings`);
  }

  /**
   * Clear all embeddings
   */
  async clearAllEmbeddings(): Promise<void> {
    if (!this.isInitialized) {
      throw new Error('Embedding service not initialized');
    }

    if ('clear' in this.vectorDatabase && typeof this.vectorDatabase.clear === 'function') {
      await this.vectorDatabase.clear();
      console.log('Cleared all embeddings');
    } else {
      throw new Error('Clear operation not supported by this vector database');
    }
  }
}
