import { IndexFlatIP } from 'faiss-node';
import { writeFile, readFile, mkdir } from 'fs/promises';
import { existsSync } from 'fs';
import { join } from 'path';
import { 
  VectorDatabase, 
  VectorDatabaseConfig, 
  EmbeddingVector, 
  SearchQuery, 
  SearchResult, 
  DatabaseStats 
} from '../types.js';

export class FAISSVectorDatabase implements VectorDatabase {
  private index: IndexFlatIP | null = null;
  private vectors: Map<string, EmbeddingVector> = new Map();
  private config: VectorDatabaseConfig;
  private indexPath: string;
  private metadataPath: string;
  private isInitialized = false;

  constructor(config: VectorDatabaseConfig) {
    this.config = config;
    this.indexPath = join(process.cwd(), 'data', 'faiss', `${config.indexName || 'logs'}.index`);
    this.metadataPath = join(process.cwd(), 'data', 'faiss', `${config.indexName || 'logs'}.metadata.json`);
  }

  async initialize(): Promise<void> {
    try {
      // Create directory if it doesn't exist
      const dir = join(process.cwd(), 'data', 'faiss');
      if (!existsSync(dir)) {
        await mkdir(dir, { recursive: true });
      }

      // Initialize FAISS index
      this.index = new IndexFlatIP(this.config.dimension);

      // Load existing data if available
      await this.loadExistingData();

      this.isInitialized = true;
      console.log(`FAISS database initialized with dimension ${this.config.dimension}`);
    } catch (error) {
      throw new Error(`Failed to initialize FAISS database: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private async loadExistingData(): Promise<void> {
    try {
      if (existsSync(this.metadataPath)) {
        const metadataContent = await readFile(this.metadataPath, 'utf-8');
        const metadata = JSON.parse(metadataContent);
        
        // Restore vectors map
        for (const [id, vectorData] of Object.entries(metadata.vectors)) {
          this.vectors.set(id, vectorData as EmbeddingVector);
        }

        // Rebuild FAISS index
        if (this.vectors.size > 0) {
          const vectorArray: number[][] = [];
          for (const vector of this.vectors.values()) {
            vectorArray.push(vector.vector);
          }
          
          // Add all vectors to the index
          for (const vector of vectorArray) {
            this.index!.add(vector);
          }

          console.log(`Loaded ${this.vectors.size} existing vectors from metadata`);
        }
      }
    } catch (error) {
      console.warn(`Could not load existing data: ${error instanceof Error ? error.message : 'Unknown error'}`);
      // Continue with empty database
    }
  }

  async insert(vectors: EmbeddingVector[]): Promise<void> {
    if (!this.isInitialized || !this.index) {
      throw new Error('Database not initialized');
    }

    try {
      for (const vector of vectors) {
        // Add to FAISS index
        this.index.add(vector.vector);
        
        // Store metadata
        this.vectors.set(vector.id, vector);
      }

      // Persist data
      await this.persistData();

      console.log(`Inserted ${vectors.length} vectors into FAISS database`);
    } catch (error) {
      throw new Error(`Failed to insert vectors: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async search(query: SearchQuery): Promise<SearchResult[]> {
    if (!this.isInitialized || !this.index) {
      throw new Error('Database not initialized');
    }

    try {
      // For FAISS, we need the query vector, not text
      // This should be handled by the calling code
      if (!('vector' in query) || !Array.isArray((query as any).vector)) {
        throw new Error('FAISS search requires a vector in the query');
      }

      const queryVector = (query as any).vector as number[];
      const limit = query.limit || 10;
      const threshold = query.threshold || 0.7;

      // Perform search
      const searchResult = this.index.search(queryVector, limit);
      
      const results: SearchResult[] = [];
      const vectorIds = Array.from(this.vectors.keys());

      for (let i = 0; i < searchResult.labels.length; i++) {
        const score = searchResult.distances[i];
        const vectorIndex = searchResult.labels[i];
        
        // Skip results below threshold
        if (score < threshold) {
          continue;
        }

        // Get the vector ID from our mapping
        const vectorId = vectorIds[vectorIndex];
        const vector = this.vectors.get(vectorId);

        if (vector) {
          // Apply metadata filters if specified
          if (query.filters && !this.matchesFilters(vector.metadata, query.filters)) {
            continue;
          }

          results.push({
            id: vector.id,
            score,
            metadata: vector.metadata,
            text: vector.text,
            vector: query.includeMetadata ? vector.vector : undefined
          });
        }
      }

      return results.sort((a, b) => b.score - a.score);
    } catch (error) {
      throw new Error(`Failed to search vectors: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private matchesFilters(metadata: any, filters: any): boolean {
    for (const [key, value] of Object.entries(filters)) {
      if (value !== undefined && metadata[key] !== value) {
        return false;
      }
    }
    return true;
  }

  async delete(ids: string[]): Promise<void> {
    if (!this.isInitialized) {
      throw new Error('Database not initialized');
    }

    try {
      // Remove from metadata
      for (const id of ids) {
        this.vectors.delete(id);
      }

      // Rebuild FAISS index (FAISS doesn't support individual deletions)
      this.index = new IndexFlatIP(this.config.dimension);
      
      if (this.vectors.size > 0) {
        for (const vector of this.vectors.values()) {
          this.index.add(vector.vector);
        }
      }

      // Persist changes
      await this.persistData();

      console.log(`Deleted ${ids.length} vectors from FAISS database`);
    } catch (error) {
      throw new Error(`Failed to delete vectors: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async getStats(): Promise<DatabaseStats> {
    if (!this.isInitialized || !this.index) {
      throw new Error('Database not initialized');
    }

    return {
      totalVectors: this.vectors.size,
      dimension: this.config.dimension,
      indexSize: this.index.ntotal(),
      lastUpdated: new Date()
    };
  }

  async close(): Promise<void> {
    if (this.isInitialized) {
      await this.persistData();
      this.index = null;
      this.vectors.clear();
      this.isInitialized = false;
      console.log('FAISS database closed');
    }
  }

  private async persistData(): Promise<void> {
    try {
      // Save metadata
      const metadata = {
        config: this.config,
        vectors: Object.fromEntries(this.vectors),
        lastUpdated: new Date().toISOString()
      };

      await writeFile(this.metadataPath, JSON.stringify(metadata, null, 2));
    } catch (error) {
      console.error(`Failed to persist data: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get all vectors (for debugging/export)
   */
  getAllVectors(): EmbeddingVector[] {
    return Array.from(this.vectors.values());
  }

  /**
   * Clear all data
   */
  async clear(): Promise<void> {
    if (!this.isInitialized) {
      throw new Error('Database not initialized');
    }

    this.vectors.clear();
    this.index = new IndexFlatIP(this.config.dimension);
    await this.persistData();
    
    console.log('FAISS database cleared');
  }
}
