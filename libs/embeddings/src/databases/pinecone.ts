import { Pinecone } from '@pinecone-database/pinecone';
import { 
  VectorDatabase, 
  VectorDatabaseConfig, 
  EmbeddingVector, 
  SearchQuery, 
  SearchResult, 
  DatabaseStats 
} from '../types.js';

export class PineconeVectorDatabase implements VectorDatabase {
  private client: Pinecone | null = null;
  private index: any = null;
  private config: VectorDatabaseConfig;
  private isInitialized = false;

  constructor(config: VectorDatabaseConfig) {
    this.config = config;
    
    if (!config.apiKey) {
      throw new Error('Pinecone API key is required');
    }
    
    if (!config.indexName) {
      throw new Error('Pinecone index name is required');
    }
  }

  async initialize(): Promise<void> {
    try {
      this.client = new Pinecone({
        apiKey: this.config.apiKey!,
        environment: this.config.environment || 'us-east1-gcp',
      });

      // Get the index
      this.index = this.client.index(this.config.indexName!);

      // Verify the index exists and is ready
      await this.verifyIndex();

      this.isInitialized = true;
      console.log(`Pinecone database initialized with index: ${this.config.indexName}`);
    } catch (error) {
      throw new Error(`Failed to initialize Pinecone database: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private async verifyIndex(): Promise<void> {
    try {
      const indexStats = await this.index.describeIndexStats();
      console.log(`Pinecone index stats:`, indexStats);
    } catch (error) {
      throw new Error(`Failed to verify Pinecone index: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async insert(vectors: EmbeddingVector[]): Promise<void> {
    if (!this.isInitialized || !this.index) {
      throw new Error('Database not initialized');
    }

    try {
      // Convert to Pinecone format
      const pineconeVectors = vectors.map(vector => ({
        id: vector.id,
        values: vector.vector,
        metadata: {
          ...vector.metadata,
          text: vector.text,
          createdAt: vector.createdAt.toISOString()
        }
      }));

      // Batch upsert (Pinecone handles batching internally)
      const batchSize = 100; // Pinecone recommended batch size
      
      for (let i = 0; i < pineconeVectors.length; i += batchSize) {
        const batch = pineconeVectors.slice(i, i + batchSize);
        await this.index.upsert(batch);
        
        // Small delay between batches to respect rate limits
        if (i + batchSize < pineconeVectors.length) {
          await new Promise(resolve => setTimeout(resolve, 100));
        }
      }

      console.log(`Inserted ${vectors.length} vectors into Pinecone database`);
    } catch (error) {
      throw new Error(`Failed to insert vectors: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async search(query: SearchQuery): Promise<SearchResult[]> {
    if (!this.isInitialized || !this.index) {
      throw new Error('Database not initialized');
    }

    try {
      // For Pinecone, we need the query vector
      if (!('vector' in query) || !Array.isArray((query as any).vector)) {
        throw new Error('Pinecone search requires a vector in the query');
      }

      const queryVector = (query as any).vector as number[];
      const limit = query.limit || 10;
      const threshold = query.threshold || 0.7;

      // Build filter for metadata
      const filter = this.buildMetadataFilter(query.filters);

      // Perform search
      const searchResponse = await this.index.query({
        vector: queryVector,
        topK: limit,
        includeMetadata: true,
        includeValues: query.includeMetadata || false,
        filter
      });

      const results: SearchResult[] = [];

      for (const match of searchResponse.matches || []) {
        // Skip results below threshold
        if (match.score < threshold) {
          continue;
        }

        const metadata = match.metadata || {};
        
        results.push({
          id: match.id,
          score: match.score,
          metadata: {
            timestamp: metadata.timestamp,
            level: metadata.level,
            module: metadata.module,
            sessionId: metadata.sessionId,
            platform: metadata.platform,
            customerId: metadata.customerId,
            clientIp: metadata.clientIp,
            requestId: metadata.requestId,
            auditType: metadata.auditType,
            appName: metadata.appName,
            responseTime: metadata.responseTime,
            status: metadata.status,
            method: metadata.method,
            originUrl: metadata.originUrl,
            sourceFile: metadata.sourceFile,
            lineNumber: metadata.lineNumber
          },
          text: metadata.text || '',
          vector: match.values
        });
      }

      return results.sort((a, b) => b.score - a.score);
    } catch (error) {
      throw new Error(`Failed to search vectors: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private buildMetadataFilter(filters?: any): any {
    if (!filters) {
      return undefined;
    }

    const filter: any = {};

    // Build Pinecone-compatible filter
    for (const [key, value] of Object.entries(filters)) {
      if (value !== undefined) {
        filter[key] = { $eq: value };
      }
    }

    return Object.keys(filter).length > 0 ? filter : undefined;
  }

  async delete(ids: string[]): Promise<void> {
    if (!this.isInitialized || !this.index) {
      throw new Error('Database not initialized');
    }

    try {
      // Batch delete
      const batchSize = 1000; // Pinecone supports up to 1000 IDs per delete
      
      for (let i = 0; i < ids.length; i += batchSize) {
        const batch = ids.slice(i, i + batchSize);
        await this.index.deleteMany(batch);
        
        // Small delay between batches
        if (i + batchSize < ids.length) {
          await new Promise(resolve => setTimeout(resolve, 100));
        }
      }

      console.log(`Deleted ${ids.length} vectors from Pinecone database`);
    } catch (error) {
      throw new Error(`Failed to delete vectors: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async getStats(): Promise<DatabaseStats> {
    if (!this.isInitialized || !this.index) {
      throw new Error('Database not initialized');
    }

    try {
      const stats = await this.index.describeIndexStats();
      
      return {
        totalVectors: stats.totalVectorCount || 0,
        dimension: this.config.dimension,
        indexSize: stats.indexFullness || 0,
        lastUpdated: new Date()
      };
    } catch (error) {
      throw new Error(`Failed to get database stats: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async close(): Promise<void> {
    if (this.isInitialized) {
      this.client = null;
      this.index = null;
      this.isInitialized = false;
      console.log('Pinecone database connection closed');
    }
  }

  /**
   * Clear all vectors from the index
   */
  async clear(): Promise<void> {
    if (!this.isInitialized || !this.index) {
      throw new Error('Database not initialized');
    }

    try {
      await this.index.deleteAll();
      console.log('Pinecone database cleared');
    } catch (error) {
      throw new Error(`Failed to clear database: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Create a new index (for setup/admin purposes)
   */
  async createIndex(): Promise<void> {
    if (!this.client) {
      throw new Error('Pinecone client not initialized');
    }

    try {
      await this.client.createIndex({
        name: this.config.indexName!,
        dimension: this.config.dimension,
        metric: this.config.metricType || 'cosine',
      });

      console.log(`Created Pinecone index: ${this.config.indexName}`);
    } catch (error) {
      throw new Error(`Failed to create index: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Delete the index (for cleanup/admin purposes)
   */
  async deleteIndex(): Promise<void> {
    if (!this.client) {
      throw new Error('Pinecone client not initialized');
    }

    try {
      await this.client.deleteIndex(this.config.indexName!);
      console.log(`Deleted Pinecone index: ${this.config.indexName}`);
    } catch (error) {
      throw new Error(`Failed to delete index: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
}
