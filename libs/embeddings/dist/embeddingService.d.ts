import { EmbeddingProvider, VectorDatabase, SearchQuery, SearchResult, BatchEmbeddingResult } from './types.js';
export declare class EmbeddingService {
    private embeddingProvider;
    private vectorDatabase;
    private isInitialized;
    constructor(embeddingProvider: EmbeddingProvider, vectorDatabase: VectorDatabase);
    initialize(): Promise<void>;
    /**
     * Process and store log entries as embeddings
     */
    processLogEntries(logEntries: any[]): Promise<BatchEmbeddingResult>;
    /**
     * Search for similar log entries using natural language query
     */
    searchLogs(queryText: string, options?: Partial<SearchQuery>): Promise<SearchResult[]>;
    /**
     * Find similar log entries to a given log entry
     */
    findSimilarLogs(logEntry: any, options?: Partial<SearchQuery>): Promise<SearchResult[]>;
    /**
     * Get database statistics
     */
    getStats(): Promise<import("./types.js").DatabaseStats>;
    /**
     * Close the service and cleanup resources
     */
    close(): Promise<void>;
    private extractMetadata;
    /**
     * Delete embeddings by IDs
     */
    deleteEmbeddings(ids: string[]): Promise<void>;
    /**
     * Clear all embeddings
     */
    clearAllEmbeddings(): Promise<void>;
}
//# sourceMappingURL=embeddingService.d.ts.map