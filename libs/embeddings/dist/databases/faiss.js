import { writeFile, readFile, mkdir } from 'fs/promises';
import { existsSync } from 'fs';
import { join } from 'path';
export class FAISSVectorDatabase {
    index = null;
    vectors = new Map();
    config;
    indexPath;
    metadataPath;
    isInitialized = false;
    constructor(config) {
        this.config = config;
        this.indexPath = join(process.cwd(), 'data', 'faiss', `${config.indexName || 'logs'}.index`);
        this.metadataPath = join(process.cwd(), 'data', 'faiss', `${config.indexName || 'logs'}.metadata.json`);
    }
    async initialize() {
        try {
            // Create directory if it doesn't exist
            const dir = join(process.cwd(), 'data', 'faiss');
            if (!existsSync(dir)) {
                await mkdir(dir, { recursive: true });
            }
            // For now, use a simple in-memory implementation
            // TODO: Implement proper FAISS integration when faiss-node is properly configured
            this.index = {
                vectors: [],
                add: (vector) => {
                    this.index.vectors.push(vector);
                },
                search: (queryVector, k) => {
                    // Simple cosine similarity search
                    const similarities = this.index.vectors.map((vector, idx) => ({
                        index: idx,
                        similarity: this.cosineSimilarity(queryVector, vector)
                    }));
                    similarities.sort((a, b) => b.similarity - a.similarity);
                    return {
                        labels: similarities.slice(0, k).map((s) => s.index),
                        distances: similarities.slice(0, k).map((s) => s.similarity)
                    };
                },
                ntotal: () => this.index.vectors.length
            };
            // Load existing data if available
            await this.loadExistingData();
            this.isInitialized = true;
            console.log(`FAISS database initialized with dimension ${this.config.dimension}`);
        }
        catch (error) {
            throw new Error(`Failed to initialize FAISS database: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    async loadExistingData() {
        try {
            if (existsSync(this.metadataPath)) {
                const metadataContent = await readFile(this.metadataPath, 'utf-8');
                const metadata = JSON.parse(metadataContent);
                // Restore vectors map
                for (const [id, vectorData] of Object.entries(metadata.vectors)) {
                    this.vectors.set(id, vectorData);
                }
                // Rebuild FAISS index
                if (this.vectors.size > 0) {
                    const vectorArray = [];
                    for (const vector of this.vectors.values()) {
                        vectorArray.push(vector.vector);
                    }
                    // Add all vectors to the index
                    for (const vector of vectorArray) {
                        this.index.add(vector);
                    }
                    console.log(`Loaded ${this.vectors.size} existing vectors from metadata`);
                }
            }
        }
        catch (error) {
            console.warn(`Could not load existing data: ${error instanceof Error ? error.message : 'Unknown error'}`);
            // Continue with empty database
        }
    }
    async insert(vectors) {
        if (!this.isInitialized || !this.index) {
            throw new Error('Database not initialized');
        }
        try {
            for (const vector of vectors) {
                // Add to FAISS index
                this.index.add(vector.vector);
                // Store metadata
                this.vectors.set(vector.id, vector);
            }
            // Persist data
            await this.persistData();
            console.log(`Inserted ${vectors.length} vectors into FAISS database`);
        }
        catch (error) {
            throw new Error(`Failed to insert vectors: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    async search(query) {
        if (!this.isInitialized || !this.index) {
            throw new Error('Database not initialized');
        }
        try {
            // For FAISS, we need the query vector, not text
            // This should be handled by the calling code
            if (!('vector' in query) || !Array.isArray(query.vector)) {
                throw new Error('FAISS search requires a vector in the query');
            }
            const queryVector = query.vector;
            const limit = query.limit || 10;
            const threshold = query.threshold || 0.7;
            // Perform search
            const searchResult = this.index.search(queryVector, limit);
            const results = [];
            const vectorIds = Array.from(this.vectors.keys());
            for (let i = 0; i < searchResult.labels.length; i++) {
                const score = searchResult.distances[i];
                const vectorIndex = searchResult.labels[i];
                // Skip results below threshold
                if (score < threshold) {
                    continue;
                }
                // Get the vector ID from our mapping
                const vectorId = vectorIds[vectorIndex];
                const vector = this.vectors.get(vectorId);
                if (vector) {
                    // Apply metadata filters if specified
                    if (query.filters && !this.matchesFilters(vector.metadata, query.filters)) {
                        continue;
                    }
                    results.push({
                        id: vector.id,
                        score,
                        metadata: vector.metadata,
                        text: vector.text,
                        vector: query.includeMetadata ? vector.vector : undefined
                    });
                }
            }
            return results.sort((a, b) => b.score - a.score);
        }
        catch (error) {
            throw new Error(`Failed to search vectors: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    matchesFilters(metadata, filters) {
        for (const [key, value] of Object.entries(filters)) {
            if (value !== undefined && metadata[key] !== value) {
                return false;
            }
        }
        return true;
    }
    async delete(ids) {
        if (!this.isInitialized) {
            throw new Error('Database not initialized');
        }
        try {
            // Remove from metadata
            for (const id of ids) {
                this.vectors.delete(id);
            }
            // Rebuild index (remove deleted vectors)
            this.index.vectors = [];
            if (this.vectors.size > 0) {
                for (const vector of this.vectors.values()) {
                    this.index.add(vector.vector);
                }
            }
            // Persist changes
            await this.persistData();
            console.log(`Deleted ${ids.length} vectors from FAISS database`);
        }
        catch (error) {
            throw new Error(`Failed to delete vectors: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    async getStats() {
        if (!this.isInitialized || !this.index) {
            throw new Error('Database not initialized');
        }
        return {
            totalVectors: this.vectors.size,
            dimension: this.config.dimension,
            indexSize: this.index.ntotal(),
            lastUpdated: new Date()
        };
    }
    async close() {
        if (this.isInitialized) {
            await this.persistData();
            this.index = null;
            this.vectors.clear();
            this.isInitialized = false;
            console.log('FAISS database closed');
        }
    }
    async persistData() {
        try {
            // Save metadata
            const metadata = {
                config: this.config,
                vectors: Object.fromEntries(this.vectors),
                lastUpdated: new Date().toISOString()
            };
            await writeFile(this.metadataPath, JSON.stringify(metadata, null, 2));
        }
        catch (error) {
            console.error(`Failed to persist data: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    /**
     * Get all vectors (for debugging/export)
     */
    getAllVectors() {
        return Array.from(this.vectors.values());
    }
    /**
     * Clear all data
     */
    async clear() {
        if (!this.isInitialized) {
            throw new Error('Database not initialized');
        }
        this.vectors.clear();
        this.index.vectors = [];
        await this.persistData();
        console.log('FAISS database cleared');
    }
    cosineSimilarity(a, b) {
        if (a.length !== b.length) {
            return 0;
        }
        let dotProduct = 0;
        let normA = 0;
        let normB = 0;
        for (let i = 0; i < a.length; i++) {
            dotProduct += a[i] * b[i];
            normA += a[i] * a[i];
            normB += b[i] * b[i];
        }
        normA = Math.sqrt(normA);
        normB = Math.sqrt(normB);
        if (normA === 0 || normB === 0) {
            return 0;
        }
        return dotProduct / (normA * normB);
    }
}
//# sourceMappingURL=faiss.js.map