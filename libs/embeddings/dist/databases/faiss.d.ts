import { VectorDatabase, VectorDatabaseConfig, EmbeddingVector, SearchQuery, SearchResult, DatabaseStats } from '../types.js';
export declare class FAISSVectorDatabase implements VectorDatabase {
    private index;
    private vectors;
    private config;
    private indexPath;
    private metadataPath;
    private isInitialized;
    constructor(config: VectorDatabaseConfig);
    initialize(): Promise<void>;
    private loadExistingData;
    insert(vectors: EmbeddingVector[]): Promise<void>;
    search(query: SearchQuery): Promise<SearchResult[]>;
    private matchesFilters;
    delete(ids: string[]): Promise<void>;
    getStats(): Promise<DatabaseStats>;
    close(): Promise<void>;
    private persistData;
    /**
     * Get all vectors (for debugging/export)
     */
    getAllVectors(): EmbeddingVector[];
    /**
     * Clear all data
     */
    clear(): Promise<void>;
    private cosineSimilarity;
}
//# sourceMappingURL=faiss.d.ts.map