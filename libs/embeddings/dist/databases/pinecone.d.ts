import { VectorDatabase, VectorDatabaseConfig, EmbeddingVector, SearchQuery, SearchResult, DatabaseStats } from '../types.js';
export declare class PineconeVectorDatabase implements VectorDatabase {
    private client;
    private index;
    private config;
    private isInitialized;
    constructor(config: VectorDatabaseConfig);
    initialize(): Promise<void>;
    private verifyIndex;
    insert(vectors: EmbeddingVector[]): Promise<void>;
    search(query: SearchQuery): Promise<SearchResult[]>;
    private buildMetadataFilter;
    delete(ids: string[]): Promise<void>;
    getStats(): Promise<DatabaseStats>;
    close(): Promise<void>;
    /**
     * Clear all vectors from the index
     */
    clear(): Promise<void>;
    /**
     * Create a new index (for setup/admin purposes)
     */
    createIndex(): Promise<void>;
    /**
     * Delete the index (for cleanup/admin purposes)
     */
    deleteIndex(): Promise<void>;
}
//# sourceMappingURL=pinecone.d.ts.map