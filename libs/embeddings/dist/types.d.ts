export interface EmbeddingVector {
    id: string;
    vector: number[];
    metadata: LogMetadata;
    text: string;
    createdAt: Date;
}
export interface LogMetadata {
    timestamp: string;
    level: string;
    module: string;
    sessionId: string;
    platform: string;
    customerId: string;
    clientIp: string;
    requestId: string;
    auditType: string;
    appName: string;
    responseTime: number;
    status: number;
    method: string;
    originUrl: string;
    sourceFile?: string;
    lineNumber?: number;
}
export interface SearchQuery {
    text: string;
    filters?: Partial<LogMetadata>;
    limit?: number;
    threshold?: number;
    includeMetadata?: boolean;
}
export interface SearchResult {
    id: string;
    score: number;
    metadata: LogMetadata;
    text: string;
    vector?: number[];
}
export interface VectorDatabaseConfig {
    provider: 'faiss' | 'pinecone';
    dimension: number;
    indexName?: string;
    apiKey?: string;
    environment?: string;
    metricType?: 'cosine' | 'euclidean' | 'dotproduct';
}
export interface EmbeddingConfig {
    provider: 'openai' | 'sentence-transformers';
    model: string;
    apiKey?: string;
    maxTokens?: number;
    batchSize?: number;
}
export interface BatchEmbeddingResult {
    successful: number;
    failed: number;
    embeddings: EmbeddingVector[];
    errors: Array<{
        id: string;
        error: string;
    }>;
}
export interface VectorDatabase {
    initialize(): Promise<void>;
    insert(vectors: EmbeddingVector[]): Promise<void>;
    search(query: SearchQuery): Promise<SearchResult[]>;
    delete(ids: string[]): Promise<void>;
    getStats(): Promise<DatabaseStats>;
    close(): Promise<void>;
}
export interface DatabaseStats {
    totalVectors: number;
    dimension: number;
    indexSize: number;
    lastUpdated: Date;
}
export interface EmbeddingProvider {
    generateEmbedding(text: string): Promise<number[]>;
    generateBatchEmbeddings(texts: string[]): Promise<number[][]>;
    getDimension(): number;
    getMaxTokens(): number;
}
//# sourceMappingURL=types.d.ts.map