import { EmbeddingProvider, EmbeddingConfig } from '../types.js';
export declare class OpenAIEmbeddingProvider implements EmbeddingProvider {
    private client;
    private model;
    private maxTokens;
    private dimension;
    constructor(config: EmbeddingConfig);
    private getDimensionForModel;
    generateEmbedding(text: string): Promise<number[]>;
    generateBatchEmbeddings(texts: string[]): Promise<number[][]>;
    getDimension(): number;
    getMaxTokens(): number;
    private truncateText;
    /**
     * Prepare log text for embedding generation
     */
    static prepareLogText(logEntry: any): string;
    /**
     * Prepare search query text for embedding
     */
    static prepareQueryText(query: string): string;
    /**
     * Calculate cosine similarity between two vectors
     */
    static cosineSimilarity(a: number[], b: number[]): number;
}
//# sourceMappingURL=openai.d.ts.map