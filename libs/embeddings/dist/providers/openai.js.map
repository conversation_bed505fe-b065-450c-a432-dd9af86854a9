{"version": 3, "file": "openai.js", "sourceRoot": "", "sources": ["../../src/providers/openai.ts"], "names": [], "mappings": "AAAA,OAAO,MAAM,MAAM,QAAQ,CAAC;AAG5B,MAAM,OAAO,uBAAuB;IAC1B,MAAM,CAAS;IACf,KAAK,CAAS;IACd,SAAS,CAAS;IAClB,SAAS,CAAS;IAE1B,YAAY,MAAuB;QACjC,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;YACnB,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAC;QAChD,CAAC;QAED,IAAI,CAAC,MAAM,GAAG,IAAI,MAAM,CAAC;YACvB,MAAM,EAAE,MAAM,CAAC,MAAM;SACtB,CAAC,CAAC;QAEH,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,IAAI,wBAAwB,CAAC;QACtD,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC,SAAS,IAAI,IAAI,CAAC;QAE1C,+BAA+B;QAC/B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IACzD,CAAC;IAEO,oBAAoB,CAAC,KAAa;QACxC,MAAM,eAAe,GAA2B;YAC9C,wBAAwB,EAAE,IAAI;YAC9B,wBAAwB,EAAE,IAAI;YAC9B,wBAAwB,EAAE,IAAI;SAC/B,CAAC;QAEF,OAAO,eAAe,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC;IACxC,CAAC;IAED,KAAK,CAAC,iBAAiB,CAAC,IAAY;QAClC,IAAI,CAAC;YACH,4DAA4D;YAC5D,MAAM,aAAa,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;YAE9C,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC;gBACnD,KAAK,EAAE,IAAI,CAAC,KAAK;gBACjB,KAAK,EAAE,aAAa;gBACpB,eAAe,EAAE,OAAO;aACzB,CAAC,CAAC;YAEH,IAAI,CAAC,QAAQ,CAAC,IAAI,IAAI,QAAQ,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACjD,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAC;YAC5D,CAAC;YAED,OAAO,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;QACpC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,iCAAiC,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE,CAAC,CAAC;QAC/G,CAAC;IACH,CAAC;IAED,KAAK,CAAC,uBAAuB,CAAC,KAAe;QAC3C,IAAI,CAAC;YACH,qEAAqE;YACrE,MAAM,SAAS,GAAG,GAAG,CAAC,CAAC,0BAA0B;YACjD,MAAM,OAAO,GAAe,EAAE,CAAC;YAE/B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,IAAI,SAAS,EAAE,CAAC;gBACjD,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,CAAC;gBAC5C,MAAM,cAAc,GAAG,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC;gBAElE,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC;oBACnD,KAAK,EAAE,IAAI,CAAC,KAAK;oBACjB,KAAK,EAAE,cAAc;oBACrB,eAAe,EAAE,OAAO;iBACzB,CAAC,CAAC;gBAEH,IAAI,CAAC,QAAQ,CAAC,IAAI,IAAI,QAAQ,CAAC,IAAI,CAAC,MAAM,KAAK,KAAK,CAAC,MAAM,EAAE,CAAC;oBAC5D,MAAM,IAAI,KAAK,CAAC,oCAAoC,KAAK,CAAC,MAAM,oBAAoB,QAAQ,CAAC,IAAI,EAAE,MAAM,IAAI,CAAC,EAAE,CAAC,CAAC;gBACpH,CAAC;gBAED,MAAM,eAAe,GAAG,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;gBAClE,OAAO,CAAC,IAAI,CAAC,GAAG,eAAe,CAAC,CAAC;gBAEjC,2CAA2C;gBAC3C,IAAI,CAAC,GAAG,SAAS,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC;oBACjC,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC;gBACzD,CAAC;YACH,CAAC;YAED,OAAO,OAAO,CAAC;QACjB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,wCAAwC,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE,CAAC,CAAC;QACtH,CAAC;IACH,CAAC;IAED,YAAY;QACV,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IAED,YAAY;QACV,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IAEO,YAAY,CAAC,IAAY;QAC/B,4DAA4D;QAC5D,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC;QAEpC,IAAI,IAAI,CAAC,MAAM,IAAI,QAAQ,EAAE,CAAC;YAC5B,OAAO,IAAI,CAAC;QACd,CAAC;QAED,4BAA4B;QAC5B,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,QAAQ,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC;IACjD,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,cAAc,CAAC,QAAa;QACjC,8DAA8D;QAC9D,MAAM,KAAK,GAAG;YACZ,UAAU,QAAQ,CAAC,KAAK,EAAE;YAC1B,WAAW,QAAQ,CAAC,MAAM,EAAE;YAC5B,WAAW,QAAQ,CAAC,MAAM,EAAE;YAC5B,WAAW,QAAQ,CAAC,MAAM,EAAE;YAC5B,kBAAkB,QAAQ,CAAC,YAAY,IAAI;YAC3C,aAAa,QAAQ,CAAC,QAAQ,EAAE;YAChC,eAAe,QAAQ,CAAC,SAAS,EAAE;YACnC,QAAQ,QAAQ,CAAC,OAAO,EAAE;YAC1B,QAAQ,QAAQ,CAAC,SAAS,EAAE;SAC7B,CAAC;QAEF,8BAA8B;QAC9B,IAAI,QAAQ,CAAC,SAAS,IAAI,QAAQ,CAAC,SAAS,CAAC,IAAI,EAAE,EAAE,CAAC;YACpD,KAAK,CAAC,IAAI,CAAC,UAAU,QAAQ,CAAC,SAAS,EAAE,CAAC,CAAC;QAC7C,CAAC;QAED,4BAA4B;QAC5B,IAAI,QAAQ,CAAC,OAAO,IAAI,QAAQ,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC;YAChD,KAAK,CAAC,IAAI,CAAC,SAAS,QAAQ,CAAC,OAAO,EAAE,CAAC,CAAC;QAC1C,CAAC;QAED,OAAO,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,gBAAgB,CAAC,KAAa;QACnC,gCAAgC;QAChC,OAAO,KAAK,CAAC,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;IACpC,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,gBAAgB,CAAC,CAAW,EAAE,CAAW;QAC9C,IAAI,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC,MAAM,EAAE,CAAC;YAC1B,MAAM,IAAI,KAAK,CAAC,sCAAsC,CAAC,CAAC;QAC1D,CAAC;QAED,IAAI,UAAU,GAAG,CAAC,CAAC;QACnB,IAAI,KAAK,GAAG,CAAC,CAAC;QACd,IAAI,KAAK,GAAG,CAAC,CAAC;QAEd,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAClC,UAAU,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YAC1B,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YACrB,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC;QAED,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACzB,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAEzB,IAAI,KAAK,KAAK,CAAC,IAAI,KAAK,KAAK,CAAC,EAAE,CAAC;YAC/B,OAAO,CAAC,CAAC;QACX,CAAC;QAED,OAAO,UAAU,GAAG,CAAC,KAAK,GAAG,KAAK,CAAC,CAAC;IACtC,CAAC;CACF"}