import OpenAI from 'openai';
export class OpenAIEmbeddingProvider {
    client;
    model;
    maxTokens;
    dimension;
    constructor(config) {
        if (!config.apiKey) {
            throw new Error('OpenAI API key is required');
        }
        this.client = new OpenAI({
            apiKey: config.apiKey,
        });
        this.model = config.model || 'text-embedding-3-small';
        this.maxTokens = config.maxTokens || 8191;
        // Set dimension based on model
        this.dimension = this.getDimensionForModel(this.model);
    }
    getDimensionForModel(model) {
        const modelDimensions = {
            'text-embedding-3-small': 1536,
            'text-embedding-3-large': 3072,
            'text-embedding-ada-002': 1536,
        };
        return modelDimensions[model] || 1536;
    }
    async generateEmbedding(text) {
        try {
            // Truncate text if it exceeds max tokens (rough estimation)
            const truncatedText = this.truncateText(text);
            const response = await this.client.embeddings.create({
                model: this.model,
                input: truncatedText,
                encoding_format: 'float',
            });
            if (!response.data || response.data.length === 0) {
                throw new Error('No embedding data received from OpenAI');
            }
            return response.data[0].embedding;
        }
        catch (error) {
            throw new Error(`Failed to generate embedding: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    async generateBatchEmbeddings(texts) {
        try {
            // OpenAI has a limit on batch size, so we need to chunk the requests
            const batchSize = 100; // Conservative batch size
            const results = [];
            for (let i = 0; i < texts.length; i += batchSize) {
                const batch = texts.slice(i, i + batchSize);
                const truncatedBatch = batch.map(text => this.truncateText(text));
                const response = await this.client.embeddings.create({
                    model: this.model,
                    input: truncatedBatch,
                    encoding_format: 'float',
                });
                if (!response.data || response.data.length !== batch.length) {
                    throw new Error(`Batch embedding failed: expected ${batch.length} embeddings, got ${response.data?.length || 0}`);
                }
                const batchEmbeddings = response.data.map(item => item.embedding);
                results.push(...batchEmbeddings);
                // Add a small delay to respect rate limits
                if (i + batchSize < texts.length) {
                    await new Promise(resolve => setTimeout(resolve, 100));
                }
            }
            return results;
        }
        catch (error) {
            throw new Error(`Failed to generate batch embeddings: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    getDimension() {
        return this.dimension;
    }
    getMaxTokens() {
        return this.maxTokens;
    }
    truncateText(text) {
        // Rough estimation: 1 token ≈ 4 characters for English text
        const maxChars = this.maxTokens * 4;
        if (text.length <= maxChars) {
            return text;
        }
        // Truncate and add ellipsis
        return text.substring(0, maxChars - 3) + '...';
    }
    /**
     * Prepare log text for embedding generation
     */
    static prepareLogText(logEntry) {
        // Create a comprehensive text representation of the log entry
        const parts = [
            `Level: ${logEntry.level}`,
            `Module: ${logEntry.module}`,
            `Status: ${logEntry.status}`,
            `Method: ${logEntry.method}`,
            `Response Time: ${logEntry.responseTime}ms`,
            `Platform: ${logEntry.platform}`,
            `Audit Type: ${logEntry.auditType}`,
            `App: ${logEntry.appName}`,
            `URL: ${logEntry.originUrl}`,
        ];
        // Add extra data if available
        if (logEntry.extraData && logEntry.extraData.trim()) {
            parts.push(`Extra: ${logEntry.extraData}`);
        }
        // Add raw data if available
        if (logEntry.rawData && logEntry.rawData.trim()) {
            parts.push(`Data: ${logEntry.rawData}`);
        }
        return parts.join(' | ');
    }
    /**
     * Prepare search query text for embedding
     */
    static prepareQueryText(query) {
        // Clean and normalize the query
        return query.trim().toLowerCase();
    }
    /**
     * Calculate cosine similarity between two vectors
     */
    static cosineSimilarity(a, b) {
        if (a.length !== b.length) {
            throw new Error('Vectors must have the same dimension');
        }
        let dotProduct = 0;
        let normA = 0;
        let normB = 0;
        for (let i = 0; i < a.length; i++) {
            dotProduct += a[i] * b[i];
            normA += a[i] * a[i];
            normB += b[i] * b[i];
        }
        normA = Math.sqrt(normA);
        normB = Math.sqrt(normB);
        if (normA === 0 || normB === 0) {
            return 0;
        }
        return dotProduct / (normA * normB);
    }
}
//# sourceMappingURL=openai.js.map