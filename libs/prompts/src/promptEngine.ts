import { PromptTemplate, ALL_TEMPLATES } from './templates.js';

export interface PromptContext {
  [key: string]: any;
}

export interface ProcessedPrompt {
  prompt: string;
  template: PromptTemplate;
  context: PromptContext;
  missingVariables: string[];
}

export class PromptEngine {
  private templates: Map<string, PromptTemplate> = new Map();

  constructor() {
    // Load all templates
    ALL_TEMPLATES.forEach(template => {
      this.templates.set(template.name, template);
    });
  }

  /**
   * Get a template by name
   */
  getTemplate(name: string): PromptTemplate | undefined {
    return this.templates.get(name);
  }

  /**
   * Get all available templates
   */
  getAllTemplates(): PromptTemplate[] {
    return Array.from(this.templates.values());
  }

  /**
   * Process a template with given context
   */
  processTemplate(templateName: string, context: PromptContext): ProcessedPrompt {
    const template = this.templates.get(templateName);
    if (!template) {
      throw new Error(`Template '${templateName}' not found`);
    }

    const missingVariables = this.findMissingVariables(template, context);
    const processedPrompt = this.replaceVariables(template.template, context);

    return {
      prompt: processedPrompt,
      template,
      context,
      missingVariables
    };
  }

  /**
   * Process template for single log analysis
   */
  processSingleLogAnalysis(logEntry: any): ProcessedPrompt {
    const context: PromptContext = {
      timestamp: logEntry.timestamp?.toISOString() || 'N/A',
      level: logEntry.level || 'N/A',
      module: logEntry.module || 'N/A',
      sessionId: logEntry.sessionId || 'N/A',
      platform: logEntry.platform || 'N/A',
      customerId: logEntry.customerId || 'N/A',
      clientIp: logEntry.clientIp || 'N/A',
      requestId: logEntry.requestId || 'N/A',
      auditType: logEntry.auditType || 'N/A',
      appName: logEntry.appName || 'N/A',
      responseTime: logEntry.responseTime || 'N/A',
      status: logEntry.status || 'N/A',
      method: logEntry.method || 'N/A',
      originUrl: logEntry.originUrl || 'N/A',
      extraData: logEntry.extraData || 'N/A',
      rawData: logEntry.rawData || 'N/A'
    };

    return this.processTemplate('single_log_analysis', context);
  }

  /**
   * Process template for root cause analysis
   */
  processRootCauseAnalysis(logEntries: any[], timeRange: string, modules: string[], context: string): ProcessedPrompt {
    const formattedLogs = logEntries.map((entry, index) => {
      return `Entry ${index + 1}:
- Timestamp: ${entry.timestamp?.toISOString() || 'N/A'}
- Level: ${entry.level || 'N/A'}
- Module: ${entry.module || 'N/A'}
- Status: ${entry.status || 'N/A'}
- Response Time: ${entry.responseTime || 'N/A'}ms
- Raw Data: ${entry.rawData || 'N/A'}
`;
    }).join('\n');

    const promptContext: PromptContext = {
      logEntries: formattedLogs,
      timeRange,
      modules: modules.join(', '),
      context
    };

    return this.processTemplate('root_cause_analysis', promptContext);
  }

  /**
   * Process template for query translation
   */
  processQueryTranslation(userQuery: string): ProcessedPrompt {
    const context: PromptContext = {
      userQuery
    };

    return this.processTemplate('query_translation', context);
  }

  private findMissingVariables(template: PromptTemplate, context: PromptContext): string[] {
    const missing: string[] = [];
    
    for (const variable of template.variables) {
      if (!(variable in context) || context[variable] === undefined || context[variable] === null) {
        missing.push(variable);
      }
    }

    return missing;
  }

  private replaceVariables(template: string, context: PromptContext): string {
    let result = template;

    // Replace all {{variable}} patterns
    const variablePattern = /\{\{(\w+)\}\}/g;
    result = result.replace(variablePattern, (match, variableName) => {
      if (variableName in context && context[variableName] !== undefined && context[variableName] !== null) {
        return String(context[variableName]);
      }
      return `[MISSING: ${variableName}]`;
    });

    return result;
  }

  /**
   * Validate that all required variables are provided
   */
  validateContext(templateName: string, context: PromptContext): { valid: boolean; missingVariables: string[] } {
    const template = this.templates.get(templateName);
    if (!template) {
      throw new Error(`Template '${templateName}' not found`);
    }

    const missingVariables = this.findMissingVariables(template, context);
    return {
      valid: missingVariables.length === 0,
      missingVariables
    };
  }

  /**
   * Get template variables
   */
  getTemplateVariables(templateName: string): string[] {
    const template = this.templates.get(templateName);
    if (!template) {
      throw new Error(`Template '${templateName}' not found`);
    }
    return [...template.variables];
  }

  /**
   * Add or update a custom template
   */
  addTemplate(template: PromptTemplate): void {
    this.templates.set(template.name, template);
  }

  /**
   * Remove a template
   */
  removeTemplate(templateName: string): boolean {
    return this.templates.delete(templateName);
  }
}
