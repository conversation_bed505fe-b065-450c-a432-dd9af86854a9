export interface PromptTemplate {
  name: string;
  description: string;
  template: string;
  variables: string[];
}

export const SINGLE_LOG_ANALYSIS: PromptTemplate = {
  name: 'single_log_analysis',
  description: 'Analyze a single log entry and identify potential issues',
  template: `You are an expert log analyst. Analyze the following log entry and provide insights about potential issues, patterns, or anomalies.

Log Entry:
- Timestamp: {{timestamp}}
- Level: {{level}}
- Module: {{module}}
- Session ID: {{sessionId}}
- Platform: {{platform}}
- Customer ID: {{customerId}}
- Client IP: {{clientIp}}
- Request ID: {{requestId}}
- Audit Type: {{auditType}}
- App Name: {{appName}}
- Response Time: {{responseTime}}ms
- Status Code: {{status}}
- HTTP Method: {{method}}
- Origin URL: {{originUrl}}
- Extra Data: {{extraData}}
- Raw Data: {{rawData}}

Please analyze this log entry and provide:
1. **Issue Assessment**: Is there any indication of an error, performance issue, or anomaly?
2. **Performance Analysis**: Comment on the response time and status code
3. **Context**: What business operation does this log represent?
4. **Recommendations**: Any suggested actions or investigations
5. **Severity**: Rate the severity (LOW/MEDIUM/HIGH/CRITICAL) if any issues are found

Format your response in clear sections with actionable insights.`,
  variables: ['timestamp', 'level', 'module', 'sessionId', 'platform', 'customerId', 'clientIp', 'requestId', 'auditType', 'appName', 'responseTime', 'status', 'method', 'originUrl', 'extraData', 'rawData']
};

export const ROOT_CAUSE_ANALYSIS: PromptTemplate = {
  name: 'root_cause_analysis',
  description: 'Analyze multiple related log entries to identify root causes',
  template: `You are an expert system analyst. Analyze the following related log entries to identify the root cause of issues and provide comprehensive insights.

Related Log Entries:
{{logEntries}}

Context:
- Time Range: {{timeRange}}
- Affected Modules: {{modules}}
- Session/Customer Context: {{context}}

Please provide a comprehensive root cause analysis including:

1. **Timeline Analysis**: Sequence of events leading to the issue
2. **Root Cause Identification**: What is the primary cause of the problem?
3. **Contributing Factors**: Secondary factors that may have contributed
4. **Impact Assessment**: What systems/users were affected?
5. **Pattern Recognition**: Are there recurring patterns or trends?
6. **Correlation Analysis**: How do these log entries relate to each other?
7. **Recommendations**: 
   - Immediate actions to resolve the issue
   - Long-term preventive measures
   - Monitoring improvements
8. **Severity Assessment**: Overall impact level (LOW/MEDIUM/HIGH/CRITICAL)

Focus on actionable insights and technical details that would help developers and operations teams.`,
  variables: ['logEntries', 'timeRange', 'modules', 'context']
};

export const QUERY_TRANSLATION: PromptTemplate = {
  name: 'query_translation',
  description: 'Convert natural language queries to structured filters',
  template: `You are a query translation expert. Convert the following natural language query into structured log search filters.

User Query: "{{userQuery}}"

Available filter fields:
- timestamp (date/time range)
- level (DEBUG, INFO, WARN, ERROR, FATAL)
- module (flights, hotels, activities, pointTransfer, loyalty, common)
- sessionId (string)
- platform (web, mobile, api)
- customerId (string)
- clientIp (IP address)
- requestId (string)
- auditType (string)
- appName (string)
- responseTime (number, in milliseconds)
- status (HTTP status code: 100-599)
- method (GET, POST, PUT, DELETE, PATCH, HEAD, OPTIONS)
- originUrl (URL string)

Please provide:
1. **Structured Filters**: JSON object with appropriate filters
2. **Search Keywords**: Key terms for semantic search
3. **Time Range**: If mentioned, extract time constraints
4. **Confidence**: How confident you are in the translation (1-10)
5. **Clarification**: Any ambiguities that might need user clarification

Example Output:
\`\`\`json
{
  "filters": {
    "level": "ERROR",
    "module": "flights",
    "status": [500, 502, 503, 504],
    "responseTime": { "min": 5000 }
  },
  "keywords": ["database connection", "timeout", "failure"],
  "timeRange": {
    "start": "2024-01-15T00:00:00Z",
    "end": "2024-01-15T23:59:59Z"
  },
  "confidence": 8,
  "clarification": "Assumed 'slow' means response time > 5 seconds"
}
\`\`\``,
  variables: ['userQuery']
};

export const TREND_ANALYSIS: PromptTemplate = {
  name: 'trend_analysis',
  description: 'Analyze logs over time to identify patterns and trends',
  template: `You are a data analyst specializing in log analysis. Analyze the following log data over time to identify trends, patterns, and anomalies.

Log Data Summary:
{{logSummary}}

Time Period: {{timePeriod}}
Total Entries: {{totalEntries}}

Metrics:
- Response Time Distribution: {{responseTimeStats}}
- Status Code Distribution: {{statusCodeStats}}
- Error Rate: {{errorRate}}
- Module Activity: {{moduleStats}}
- Platform Usage: {{platformStats}}

Please provide:

1. **Trend Analysis**:
   - Performance trends (response times, error rates)
   - Usage patterns by module/platform
   - Peak activity periods

2. **Anomaly Detection**:
   - Unusual spikes or drops
   - Outliers in response times
   - Unexpected error patterns

3. **Pattern Recognition**:
   - Recurring issues
   - Seasonal patterns
   - User behavior patterns

4. **Performance Insights**:
   - Bottlenecks identification
   - Resource utilization patterns
   - Scalability concerns

5. **Recommendations**:
   - Performance optimizations
   - Monitoring improvements
   - Capacity planning suggestions

6. **Alerts & Thresholds**:
   - Suggested alert thresholds
   - Key metrics to monitor
   - Early warning indicators

Focus on actionable insights that can help improve system performance and reliability.`,
  variables: ['logSummary', 'timePeriod', 'totalEntries', 'responseTimeStats', 'statusCodeStats', 'errorRate', 'moduleStats', 'platformStats']
};

export const ANOMALY_DETECTION: PromptTemplate = {
  name: 'anomaly_detection',
  description: 'Detect and analyze anomalies in log patterns',
  template: `You are an anomaly detection specialist. Analyze the following log data to identify unusual patterns, outliers, and potential security or performance issues.

Current Log Entry:
{{currentLog}}

Historical Context:
{{historicalData}}

Baseline Metrics:
- Average Response Time: {{avgResponseTime}}ms
- Typical Error Rate: {{typicalErrorRate}}%
- Normal Request Volume: {{normalVolume}} requests/hour
- Common Status Codes: {{commonStatusCodes}}

Please analyze and provide:

1. **Anomaly Classification**:
   - Type of anomaly (performance, security, business logic, etc.)
   - Severity level (LOW/MEDIUM/HIGH/CRITICAL)
   - Confidence score (1-10)

2. **Deviation Analysis**:
   - How does this deviate from normal patterns?
   - Statistical significance of the anomaly
   - Comparison with historical baselines

3. **Potential Causes**:
   - Most likely root causes
   - System components that might be affected
   - External factors to consider

4. **Impact Assessment**:
   - User experience impact
   - System performance impact
   - Business operation impact

5. **Immediate Actions**:
   - Urgent steps to take
   - Monitoring to implement
   - Escalation recommendations

6. **Investigation Guide**:
   - Additional logs to examine
   - Metrics to correlate
   - Timeline for investigation

Provide specific, actionable recommendations based on the anomaly type and severity.`,
  variables: ['currentLog', 'historicalData', 'avgResponseTime', 'typicalErrorRate', 'normalVolume', 'commonStatusCodes']
};

export const PERFORMANCE_ANALYSIS: PromptTemplate = {
  name: 'performance_analysis',
  description: 'Analyze performance metrics and identify optimization opportunities',
  template: `You are a performance optimization expert. Analyze the following performance data from log entries to identify bottlenecks and optimization opportunities.

Performance Data:
{{performanceData}}

System Context:
- Module: {{module}}
- Time Period: {{timePeriod}}
- Request Volume: {{requestVolume}}
- User Load: {{userLoad}}

Metrics Analysis:
- Response Time Percentiles: {{responseTimePercentiles}}
- Throughput: {{throughput}} requests/second
- Error Rate: {{errorRate}}%
- Resource Utilization: {{resourceUtilization}}

Please provide:

1. **Performance Assessment**:
   - Overall system health
   - Performance bottlenecks
   - Critical performance issues

2. **Response Time Analysis**:
   - Slow endpoints identification
   - Response time distribution analysis
   - Performance degradation patterns

3. **Throughput Analysis**:
   - Peak load handling
   - Capacity limitations
   - Scalability concerns

4. **Error Analysis**:
   - Error patterns and causes
   - Impact on user experience
   - Recovery time analysis

5. **Optimization Recommendations**:
   - Code-level optimizations
   - Infrastructure improvements
   - Caching strategies
   - Database optimizations

6. **Monitoring Recommendations**:
   - Key performance indicators
   - Alert thresholds
   - Monitoring dashboards

7. **Capacity Planning**:
   - Current capacity utilization
   - Growth projections
   - Scaling recommendations

Focus on practical, implementable solutions with expected impact estimates.`,
  variables: ['performanceData', 'module', 'timePeriod', 'requestVolume', 'userLoad', 'responseTimePercentiles', 'throughput', 'errorRate', 'resourceUtilization']
};

export const SECURITY_ANALYSIS: PromptTemplate = {
  name: 'security_analysis',
  description: 'Analyze logs for security threats and suspicious activities',
  template: `You are a cybersecurity analyst. Analyze the following log data for potential security threats, suspicious activities, and security policy violations.

Log Data:
{{logData}}

Security Context:
- IP Addresses: {{ipAddresses}}
- User Sessions: {{userSessions}}
- Access Patterns: {{accessPatterns}}
- Failed Attempts: {{failedAttempts}}

Please analyze and provide:

1. **Threat Assessment**:
   - Potential security threats identified
   - Risk level (LOW/MEDIUM/HIGH/CRITICAL)
   - Attack vectors detected

2. **Suspicious Activity Detection**:
   - Unusual access patterns
   - Anomalous user behavior
   - Potential intrusion attempts

3. **IP Analysis**:
   - Suspicious IP addresses
   - Geolocation anomalies
   - Known threat intelligence matches

4. **Authentication Analysis**:
   - Failed login patterns
   - Brute force attempts
   - Session anomalies

5. **Data Access Analysis**:
   - Unauthorized access attempts
   - Data exfiltration indicators
   - Privilege escalation attempts

6. **Compliance Issues**:
   - Policy violations
   - Regulatory compliance concerns
   - Audit trail gaps

7. **Immediate Actions**:
   - Security measures to implement
   - Accounts/IPs to block
   - Incident response steps

8. **Investigation Recommendations**:
   - Additional logs to examine
   - Forensic analysis steps
   - Evidence preservation

Provide specific, actionable security recommendations with priority levels.`,
  variables: ['logData', 'ipAddresses', 'userSessions', 'accessPatterns', 'failedAttempts']
};

export const ALL_TEMPLATES = [
  SINGLE_LOG_ANALYSIS,
  ROOT_CAUSE_ANALYSIS,
  QUERY_TRANSLATION,
  TREND_ANALYSIS,
  ANOMALY_DETECTION,
  PERFORMANCE_ANALYSIS,
  SECURITY_ANALYSIS
];
