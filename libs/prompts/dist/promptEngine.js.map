{"version": 3, "file": "promptEngine.js", "sourceRoot": "", "sources": ["../src/promptEngine.ts"], "names": [], "mappings": "AAAA,OAAO,EAAkB,aAAa,EAAE,MAAM,gBAAgB,CAAC;AAa/D,MAAM,OAAO,YAAY;IACf,SAAS,GAAgC,IAAI,GAAG,EAAE,CAAC;IAE3D;QACE,qBAAqB;QACrB,aAAa,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;YAC/B,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,WAAW,CAAC,IAAY;QACtB,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IAClC,CAAC;IAED;;OAEG;IACH,eAAe;QACb,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,CAAC;IAC7C,CAAC;IAED;;OAEG;IACH,eAAe,CAAC,YAAoB,EAAE,OAAsB;QAC1D,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;QAClD,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,MAAM,IAAI,KAAK,CAAC,aAAa,YAAY,aAAa,CAAC,CAAC;QAC1D,CAAC;QAED,MAAM,gBAAgB,GAAG,IAAI,CAAC,oBAAoB,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;QACtE,MAAM,eAAe,GAAG,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;QAE1E,OAAO;YACL,MAAM,EAAE,eAAe;YACvB,QAAQ;YACR,OAAO;YACP,gBAAgB;SACjB,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,wBAAwB,CAAC,QAAa;QACpC,MAAM,OAAO,GAAkB;YAC7B,SAAS,EAAE,QAAQ,CAAC,SAAS,EAAE,WAAW,EAAE,IAAI,KAAK;YACrD,KAAK,EAAE,QAAQ,CAAC,KAAK,IAAI,KAAK;YAC9B,MAAM,EAAE,QAAQ,CAAC,MAAM,IAAI,KAAK;YAChC,SAAS,EAAE,QAAQ,CAAC,SAAS,IAAI,KAAK;YACtC,QAAQ,EAAE,QAAQ,CAAC,QAAQ,IAAI,KAAK;YACpC,UAAU,EAAE,QAAQ,CAAC,UAAU,IAAI,KAAK;YACxC,QAAQ,EAAE,QAAQ,CAAC,QAAQ,IAAI,KAAK;YACpC,SAAS,EAAE,QAAQ,CAAC,SAAS,IAAI,KAAK;YACtC,SAAS,EAAE,QAAQ,CAAC,SAAS,IAAI,KAAK;YACtC,OAAO,EAAE,QAAQ,CAAC,OAAO,IAAI,KAAK;YAClC,YAAY,EAAE,QAAQ,CAAC,YAAY,IAAI,KAAK;YAC5C,MAAM,EAAE,QAAQ,CAAC,MAAM,IAAI,KAAK;YAChC,MAAM,EAAE,QAAQ,CAAC,MAAM,IAAI,KAAK;YAChC,SAAS,EAAE,QAAQ,CAAC,SAAS,IAAI,KAAK;YACtC,SAAS,EAAE,QAAQ,CAAC,SAAS,IAAI,KAAK;YACtC,OAAO,EAAE,QAAQ,CAAC,OAAO,IAAI,KAAK;SACnC,CAAC;QAEF,OAAO,IAAI,CAAC,eAAe,CAAC,qBAAqB,EAAE,OAAO,CAAC,CAAC;IAC9D,CAAC;IAED;;OAEG;IACH,wBAAwB,CAAC,UAAiB,EAAE,SAAiB,EAAE,OAAiB,EAAE,OAAe;QAC/F,MAAM,aAAa,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE;YACpD,OAAO,SAAS,KAAK,GAAG,CAAC;eAChB,KAAK,CAAC,SAAS,EAAE,WAAW,EAAE,IAAI,KAAK;WAC3C,KAAK,CAAC,KAAK,IAAI,KAAK;YACnB,KAAK,CAAC,MAAM,IAAI,KAAK;YACrB,KAAK,CAAC,MAAM,IAAI,KAAK;mBACd,KAAK,CAAC,YAAY,IAAI,KAAK;cAChC,KAAK,CAAC,OAAO,IAAI,KAAK;CACnC,CAAC;QACE,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAEd,MAAM,aAAa,GAAkB;YACnC,UAAU,EAAE,aAAa;YACzB,SAAS;YACT,OAAO,EAAE,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC;YAC3B,OAAO;SACR,CAAC;QAEF,OAAO,IAAI,CAAC,eAAe,CAAC,qBAAqB,EAAE,aAAa,CAAC,CAAC;IACpE,CAAC;IAED;;OAEG;IACH,uBAAuB,CAAC,SAAiB;QACvC,MAAM,OAAO,GAAkB;YAC7B,SAAS;SACV,CAAC;QAEF,OAAO,IAAI,CAAC,eAAe,CAAC,mBAAmB,EAAE,OAAO,CAAC,CAAC;IAC5D,CAAC;IAEO,oBAAoB,CAAC,QAAwB,EAAE,OAAsB;QAC3E,MAAM,OAAO,GAAa,EAAE,CAAC;QAE7B,KAAK,MAAM,QAAQ,IAAI,QAAQ,CAAC,SAAS,EAAE,CAAC;YAC1C,IAAI,CAAC,CAAC,QAAQ,IAAI,OAAO,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,KAAK,SAAS,IAAI,OAAO,CAAC,QAAQ,CAAC,KAAK,IAAI,EAAE,CAAC;gBAC5F,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACzB,CAAC;QACH,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAEO,gBAAgB,CAAC,QAAgB,EAAE,OAAsB;QAC/D,IAAI,MAAM,GAAG,QAAQ,CAAC;QAEtB,oCAAoC;QACpC,MAAM,eAAe,GAAG,gBAAgB,CAAC;QACzC,MAAM,GAAG,MAAM,CAAC,OAAO,CAAC,eAAe,EAAE,CAAC,KAAK,EAAE,YAAY,EAAE,EAAE;YAC/D,IAAI,YAAY,IAAI,OAAO,IAAI,OAAO,CAAC,YAAY,CAAC,KAAK,SAAS,IAAI,OAAO,CAAC,YAAY,CAAC,KAAK,IAAI,EAAE,CAAC;gBACrG,OAAO,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC;YACvC,CAAC;YACD,OAAO,aAAa,YAAY,GAAG,CAAC;QACtC,CAAC,CAAC,CAAC;QAEH,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACH,eAAe,CAAC,YAAoB,EAAE,OAAsB;QAC1D,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;QAClD,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,MAAM,IAAI,KAAK,CAAC,aAAa,YAAY,aAAa,CAAC,CAAC;QAC1D,CAAC;QAED,MAAM,gBAAgB,GAAG,IAAI,CAAC,oBAAoB,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;QACtE,OAAO;YACL,KAAK,EAAE,gBAAgB,CAAC,MAAM,KAAK,CAAC;YACpC,gBAAgB;SACjB,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,oBAAoB,CAAC,YAAoB;QACvC,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;QAClD,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,MAAM,IAAI,KAAK,CAAC,aAAa,YAAY,aAAa,CAAC,CAAC;QAC1D,CAAC;QACD,OAAO,CAAC,GAAG,QAAQ,CAAC,SAAS,CAAC,CAAC;IACjC,CAAC;IAED;;OAEG;IACH,WAAW,CAAC,QAAwB;QAClC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;IAC9C,CAAC;IAED;;OAEG;IACH,cAAc,CAAC,YAAoB;QACjC,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;IAC7C,CAAC;CACF"}