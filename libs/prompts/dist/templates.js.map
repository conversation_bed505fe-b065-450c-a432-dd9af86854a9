{"version": 3, "file": "templates.js", "sourceRoot": "", "sources": ["../src/templates.ts"], "names": [], "mappings": "AAOA,MAAM,CAAC,MAAM,mBAAmB,GAAmB;IACjD,IAAI,EAAE,qBAAqB;IAC3B,WAAW,EAAE,0DAA0D;IACvE,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;iEA2BqD;IAC/D,SAAS,EAAE,CAAC,WAAW,EAAE,OAAO,EAAE,QAAQ,EAAE,WAAW,EAAE,UAAU,EAAE,YAAY,EAAE,UAAU,EAAE,WAAW,EAAE,WAAW,EAAE,SAAS,EAAE,cAAc,EAAE,QAAQ,EAAE,QAAQ,EAAE,WAAW,EAAE,WAAW,EAAE,SAAS,CAAC;CAC7M,CAAC;AAEF,MAAM,CAAC,MAAM,mBAAmB,GAAmB;IACjD,IAAI,EAAE,qBAAqB;IAC3B,WAAW,EAAE,8DAA8D;IAC3E,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;oGAwBwF;IAClG,SAAS,EAAE,CAAC,YAAY,EAAE,WAAW,EAAE,SAAS,EAAE,SAAS,CAAC;CAC7D,CAAC;AAEF,MAAM,CAAC,MAAM,iBAAiB,GAAmB;IAC/C,IAAI,EAAE,mBAAmB;IACzB,WAAW,EAAE,wDAAwD;IACrE,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA4CL;IACL,SAAS,EAAE,CAAC,WAAW,CAAC;CACzB,CAAC;AAEF,MAAM,CAAC,MAAM,cAAc,GAAmB;IAC5C,IAAI,EAAE,gBAAgB;IACtB,WAAW,EAAE,wDAAwD;IACrE,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uFA+C2E;IACrF,SAAS,EAAE,CAAC,YAAY,EAAE,YAAY,EAAE,cAAc,EAAE,mBAAmB,EAAE,iBAAiB,EAAE,WAAW,EAAE,aAAa,EAAE,eAAe,CAAC;CAC7I,CAAC;AAEF,MAAM,CAAC,MAAM,iBAAiB,GAAmB;IAC/C,IAAI,EAAE,mBAAmB;IACzB,WAAW,EAAE,8CAA8C;IAC3D,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;qFA8CyE;IACnF,SAAS,EAAE,CAAC,YAAY,EAAE,gBAAgB,EAAE,iBAAiB,EAAE,kBAAkB,EAAE,cAAc,EAAE,mBAAmB,CAAC;CACxH,CAAC;AAEF,MAAM,CAAC,MAAM,oBAAoB,GAAmB;IAClD,IAAI,EAAE,sBAAsB;IAC5B,WAAW,EAAE,qEAAqE;IAClF,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4EAuDgE;IAC1E,SAAS,EAAE,CAAC,iBAAiB,EAAE,QAAQ,EAAE,YAAY,EAAE,eAAe,EAAE,UAAU,EAAE,yBAAyB,EAAE,YAAY,EAAE,WAAW,EAAE,qBAAqB,CAAC;CACjK,CAAC;AAEF,MAAM,CAAC,MAAM,iBAAiB,GAAmB;IAC/C,IAAI,EAAE,mBAAmB;IACzB,WAAW,EAAE,6DAA6D;IAC1E,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4EAqDgE;IAC1E,SAAS,EAAE,CAAC,SAAS,EAAE,aAAa,EAAE,cAAc,EAAE,gBAAgB,EAAE,gBAAgB,CAAC;CAC1F,CAAC;AAEF,MAAM,CAAC,MAAM,aAAa,GAAG;IAC3B,mBAAmB;IACnB,mBAAmB;IACnB,iBAAiB;IACjB,cAAc;IACd,iBAAiB;IACjB,oBAAoB;IACpB,iBAAiB;CAClB,CAAC"}