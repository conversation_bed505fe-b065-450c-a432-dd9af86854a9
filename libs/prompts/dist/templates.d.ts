export interface PromptTemplate {
    name: string;
    description: string;
    template: string;
    variables: string[];
}
export declare const SINGLE_LOG_ANALYSIS: PromptTemplate;
export declare const ROOT_CAUSE_ANALYSIS: PromptTemplate;
export declare const QUERY_TRANSLATION: PromptTemplate;
export declare const TREND_ANALYSIS: PromptTemplate;
export declare const ANOMALY_DETECTION: PromptTemplate;
export declare const PERFORMANCE_ANALYSIS: PromptTemplate;
export declare const SECURITY_ANALYSIS: PromptTemplate;
export declare const ALL_TEMPLATES: PromptTemplate[];
//# sourceMappingURL=templates.d.ts.map