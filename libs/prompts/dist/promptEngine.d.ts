import { PromptTemplate } from './templates.js';
export interface PromptContext {
    [key: string]: any;
}
export interface ProcessedPrompt {
    prompt: string;
    template: PromptTemplate;
    context: PromptContext;
    missingVariables: string[];
}
export declare class PromptEngine {
    private templates;
    constructor();
    /**
     * Get a template by name
     */
    getTemplate(name: string): PromptTemplate | undefined;
    /**
     * Get all available templates
     */
    getAllTemplates(): PromptTemplate[];
    /**
     * Process a template with given context
     */
    processTemplate(templateName: string, context: PromptContext): ProcessedPrompt;
    /**
     * Process template for single log analysis
     */
    processSingleLogAnalysis(logEntry: any): ProcessedPrompt;
    /**
     * Process template for root cause analysis
     */
    processRootCauseAnalysis(logEntries: any[], timeRange: string, modules: string[], context: string): ProcessedPrompt;
    /**
     * Process template for query translation
     */
    processQueryTranslation(userQuery: string): ProcessedPrompt;
    private findMissingVariables;
    private replaceVariables;
    /**
     * Validate that all required variables are provided
     */
    validateContext(templateName: string, context: PromptContext): {
        valid: boolean;
        missingVariables: string[];
    };
    /**
     * Get template variables
     */
    getTemplateVariables(templateName: string): string[];
    /**
     * Add or update a custom template
     */
    addTemplate(template: PromptTemplate): void;
    /**
     * Remove a template
     */
    removeTemplate(templateName: string): boolean;
}
//# sourceMappingURL=promptEngine.d.ts.map