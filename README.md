# Log Analyzer - RAG-Powered Log Analysis Application

A comprehensive RAG-powered log analysis application similar to Kibana, built with Next.js frontend and Node.js backend. The application enables natural language querying of structured logs and provides visual analytics for response times, status codes, and module-level failures.

## 🚀 Features

### Core Functionality
- **Natural Language Log Queries**: Ask questions about your logs in plain English
- **AI-Powered Analysis**: Get intelligent insights and root cause analysis
- **Real-time Dashboard**: Visual analytics with interactive charts
- **Advanced Search**: Semantic search with similarity matching
- **Log Upload & Processing**: Batch processing of log files
- **Multi-user Support**: Role-based access control (Admin, Analyst, Viewer)

### Technical Features
- **RAG Pipeline**: Retrieval-Augmented Generation for intelligent log analysis
- **Vector Database**: FAISS or Pinecone for semantic search
- **LLM Integration**: OpenAI GPT-4 for natural language processing
- **Responsive UI**: Modern interface with dark/light mode
- **Real-time Updates**: Live dashboard with auto-refresh
- **Export Capabilities**: Download filtered results

## 📋 Log Format

The application processes logs with this specific structure:
```
[timestamp][level][module][sessionId][platform][customerId][clientIp][requestId][auditType][appName][responseTime][status][method][originUrl][extraData] : rawData
```

### Supported Log Sources
- `/log/axisLogs/flights`
- `/log/axisLogs/hotels`
- `/log/axisLogs/activities`
- `/log/axisLogs/pointTransfer`
- `/log/axisLogs/loyalty`
- `/log/axisLogs/common`

## 🏗️ Architecture

```
/
├── apps/
│   ├── frontend/          # Next.js application
│   └── backend/           # Node.js API server
├── libs/
│   ├── parsers/           # Log parsing utilities
│   ├── embeddings/        # Vector generation logic
│   ├── prompts/           # LLM prompt templates
│   └── database/          # Vector DB operations
├── config/                # Environment configurations
└── docker/                # Containerization files
```

## 🛠️ Technology Stack

### Frontend
- **Next.js 14** with App Router
- **TypeScript** for type safety
- **Tailwind CSS** for styling
- **Recharts** for data visualization
- **React Query** for state management
- **Heroicons** for icons

### Backend
- **Node.js** with Express
- **TypeScript** for type safety
- **OpenAI API** for LLM integration
- **FAISS/Pinecone** for vector database
- **JWT** for authentication
- **Multer** for file uploads
- **Winston** for logging

### Libraries
- **Log Parser**: Robust parsing with validation
- **Embeddings**: OpenAI text-embedding-3-small
- **Prompts**: Template engine for LLM prompts
- **Vector DB**: Abstraction layer for multiple providers

## 🚀 Quick Start

### Prerequisites
- Node.js 18+ and npm
- OpenAI API key
- (Optional) Pinecone account for cloud vector database

### Installation

1. **Clone the repository**
```bash
git clone <repository-url>
cd logAnalyzer3
```

2. **Install dependencies**
```bash
npm install
```

3. **Set up environment variables**
```bash
cp .env.example .env
```

Edit `.env` with your configuration:
```env
# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-4

# Vector Database (choose one)
VECTOR_DATABASE=faiss  # or 'pinecone'
PINECONE_API_KEY=your_pinecone_api_key_here
PINECONE_INDEX_NAME=log-analysis

# Authentication
JWT_SECRET=your_jwt_secret_here

# Server Configuration
PORT=3001
```

4. **Start the development servers**
```bash
# Start both frontend and backend
npm run dev

# Or start individually
npm run dev:frontend  # Frontend on http://localhost:3000
npm run dev:backend   # Backend on http://localhost:3001
```

### Demo Accounts
- **Admin**: `admin` / `admin123`
- **Analyst**: `analyst` / `analyst123`
- **Viewer**: `viewer` / `viewer123`

## 📖 Usage

### 1. Upload Log Files
- Navigate to the Upload page
- Select log files (.log, .txt)
- Monitor processing progress
- View indexing results

### 2. Search Logs
- Use natural language queries like:
  - "Show me all error logs from the flights module"
  - "Find slow requests with response time over 5 seconds"
  - "Database connection failures in the last hour"
- Adjust similarity threshold and result limits
- Click on results for detailed analysis

### 3. Dashboard Analytics
- View real-time metrics and trends
- Analyze response time distributions
- Monitor error rates by module
- Track status code patterns

### 4. AI Analysis
- Single log analysis for detailed insights
- Root cause analysis for related issues
- Anomaly detection for unusual patterns
- Performance analysis with recommendations

## 🔧 Configuration

### Vector Database Options

**FAISS (Local)**
```env
VECTOR_DATABASE=faiss
VECTOR_DIMENSION=1536
```

**Pinecone (Cloud)**
```env
VECTOR_DATABASE=pinecone
PINECONE_API_KEY=your_key
PINECONE_ENVIRONMENT=your_env
PINECONE_INDEX_NAME=log-analysis
```

### Performance Tuning
```env
LOG_BATCH_SIZE=1000
MAX_FILE_SIZE_MB=100
RATE_LIMIT_MAX_REQUESTS=100
SIMILARITY_THRESHOLD=0.7
```

## 🧪 Testing

```bash
# Run all tests
npm test

# Run frontend tests
npm run test:frontend

# Run backend tests
npm run test:backend

# Run with coverage
npm run test:coverage
```

## 📦 Building for Production

```bash
# Build all applications
npm run build

# Build individually
npm run build:frontend
npm run build:backend
```

## 🐳 Docker Deployment

```bash
# Build and run with Docker Compose
docker-compose up -d

# Or build individual containers
docker build -t log-analyzer-frontend ./apps/frontend
docker build -t log-analyzer-backend ./apps/backend
```

## 🔒 Security

- JWT-based authentication
- Role-based access control
- Input validation and sanitization
- Rate limiting on all endpoints
- Secure file upload validation
- Environment variable protection

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
- Check the documentation
- Review example queries
- Examine log format requirements
- Verify environment configuration

## 🔮 Future Enhancements

- Real-time log streaming
- Advanced anomaly detection
- Custom alert rules
- Integration with monitoring tools
- Multi-tenant support
- Advanced visualization options
