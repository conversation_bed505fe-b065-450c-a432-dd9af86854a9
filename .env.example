# Server Configuration
PORT=3001
NODE_ENV=development

# Database Configuration
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=

# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-4

# Pinecone Configuration (Alternative to FAISS)
PINECONE_API_KEY=your_pinecone_api_key_here
PINECONE_ENVIRONMENT=your_pinecone_environment
PINECONE_INDEX_NAME=log-analysis

# Authentication
JWT_SECRET=your_jwt_secret_here
JWT_EXPIRES_IN=24h

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Log Processing
LOG_BATCH_SIZE=1000
MAX_FILE_SIZE_MB=100

# Vector Database
VECTOR_DIMENSION=1536
SIMILARITY_THRESHOLD=0.7

# Monitoring
ENABLE_METRICS=true
HEALTH_CHECK_INTERVAL=30000
